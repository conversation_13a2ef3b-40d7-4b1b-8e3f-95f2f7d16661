import os
from typing import Dict, List, Optional, Any

import pandas as pd

from server import thisfolder, config
from server.com.handler.ValidationHandler import ValidationHandler
from server.com.handler.CleanerHandler import <PERSON><PERSON><PERSON><PERSON><PERSON>, ProductDataFilter
from server.com.handler.DBHandler import process_dataframes_and_insert

# Initialize handlers
cleaner = CleanerHandler()
filter_processor = ProductDataFilter()


class FileProcessHandler(ValidationHandler):
    """
    Handles the file processing pipeline for data import in a structured, step-by-step manner.
    Each step is modular and can be extended or modified independently.
    """

    def start_import(self, validation_step: int = 1) -> Dict[str, Any]:
        """
        Main entry point for the import process.
        Orchestrates the entire file processing pipeline through distinct steps.

        Args:
            validation_step (int): Indicates the validation scenario:
                1 - Primary validation (new requests from request_queue)
                2 - Secondary validation (revalidation after approval from approved_queue)

        Returns:
            Dict[str, Any]: Processing result dictionary with success status, approval requirements, and reasons.
        """
        try:
            # Log the validation step
            self.request_logger.info(f"Starting import with validation_step={validation_step}")
            self.info(logs=f"Starting import with validation_step={validation_step}")

            """
            TODO: can make the changes in the logic to use the already saved file from the input directory
            Step 1: Download the input file (only for primary validation)
            if validation_step == 1 and not self._step_download_input_file():
            """

            if not self._step_download_input_file():
                return {"success": False}

            # Step 2: Process the file and check for approval requirements
            processing_result = self._step_process_file(validation_step=validation_step)
            if not processing_result["success"]:
                return processing_result

            # Step 3: Handle approval if required (for both primary and secondary validation)
            if processing_result.get("approval_required", False):
                self._step_handle_approval(processing_result.get("reasons", []))
                # Return the processing result to trigger another approval cycle
                return processing_result

            # Step 4: For secondary validation, if no approval required, add import_type_id to result
            if validation_step == 2:
                print("Validation passed, nothing more to do here - caller will handle next steps")
                # Add import_type_id to the result for the caller to use
                return processing_result

            # Step 5: Finalize the import (only for primary validation if all checks passed)
            self._step_finalize_import()
            # Add import_type_id to the result
            return processing_result

        except Exception as e:
            self._handle_exception(e)
            return {"success": False}

    def _step_download_input_file(self) -> bool:
        """
        Step 1: Downloads the input file from storage and registers it.

        Returns:
            bool: True if download and registration were successful, False otherwise.
        """
        try:
            self.update_request_status(status_id=3, stage_id=2, sub_stage=9)
            bucket_uuid = config.get("Data", "BUCKET_UUID")
            self.ProductInputFileName = self.download_file(
                bucket_id=bucket_uuid,
                file_uuid=self.File_UUID,
                out_path=os.path.join(thisfolder, config.get("PATH", "Input")),
            )

            if not self.ProductInputFileName:
                error_msg = f"ProductInputFile does not have been downloaded to local {config.get('PATH', 'Input')}"
                self.request_logger.error(f"[[ImportRequestID]] => {error_msg}")
                self.Error = error_msg
                self.error_log(logs=error_msg)
                return False

            filename = os.path.basename(self.ProductInputFileName)
            self.request_logger.info(f"ProductInput File Downloaded in local {self.ProductInputFileName}")
            self.info(logs=f"ProductInput File Downloaded in local {filename}")
            self.result(logs=f"[[ImportRequestID:]] => InputFile has been successfully downloaded to local path: {filename}")


            # Register the file in the system
            self.add_import_filename(
                import_id=self.ImportRequestId,
                output_file=self.InputFileName,
                file_uuid=self.File_UUID,
                import_type_id=self.ImportTypeId,
                output_file_type_id=1,
            )

            # Count initial data
            self.InitiateCount = self.get_count_of_data(data_source=self.ProductInputFileName)
            self.request_logger.info(f"!!!!!!!!!!!!!!!!!!!!!!! {self.InitiateCount} !!!!!!!!!!!!!!!!!!!!!!!")

            return True

        except Exception as e:
            self._handle_exception(e)
            return False

    def _step_process_file(self, validation_step: int) -> Dict[str, Any]:
        """
        Step 2: Processes the file through conversion, mapping, validation, and data processing.
        Skips catalog ID check for new suppliers with no existing data.

        Returns:
            Dict[str, Any]: Result dictionary with success status, approval requirements, and reasons.
        """
        try:
            # Initialize approval tracking
            approval_reasons = []

            # Sub-step 2.1: Check supplier data
            if validation_step == 1:
                if self.RequestTypeId == 1 or self.RequestTypeId == 2:
                    supplier_check = self.check_supplier_has_data()
                    is_new_supplier = not supplier_check["success"] and supplier_check.get("approval_required", False)
                    if is_new_supplier:
                        approval_reasons.append(supplier_check["reason"])
                        self.request_logger.warning(f"Approval required: {supplier_check['reason']}")
                else:
                    self.request_logger.info("Skipping supplier data check for non-product related requests")

            # Sub-step 2.2: Convert file to DataFrame
            file_df = self._convert_to_dataframe()
            if file_df is None or file_df.empty:
                self.request_logger.error("[[ImportRequestID]] => ProductInputFile is Empty")
                self.error_log(logs="[[ImportRequestID]] => ProductInputFile is Empty")
                self.Status = "Canceled"
                self.Error = "ProductInputFile is Empty"
                return {"success": False}

            # Sub-step 2.3: Map headers
            header_result = self._map_headers(file_df)
            if not header_result["success"]:
                return {"success": False}
            
            if self.RequestTypeId == 1 or self.RequestTypeId == 2:
                # Check if header mapping requires approval
                if header_result.get("approval_required", False):
                    approval_reasons.append(header_result["reason"])
                    self.request_logger.warning(f"Approval required: {header_result['reason']}")
            else:
                self.request_logger.info("Skipping header mapping for non-product related requests")

            header_mapper_df = header_result["data"]

            # Sub-step 2.4: Remove duplicates
            clear_df = self._remove_duplicates(header_mapper_df)
            if clear_df.empty:
                return {"success": False}

            # Sub-step 2.5: Map columns
            column_result = self._map_columns(clear_df)
            if not column_result["success"]:
                self.Error = column_result["error"]
                self.Status = "Canceled"
                self.request_logger.error(f"ERROR: {column_result['error']}")
                self.error_log(logs=f"ERROR: {column_result['error']}")
                self.update_request_status(stage_id=2, status_id=5)
                return {"success": False}

            # Sub-step 2.6: Validate catalog (skip for new suppliers) and warehouses
            # Note: for the temporary solution, we are skipping the catalog check for new suppliers
            # TODO: Revisit this logic when we have a better understanding of the requirements
            # if True:  # remove this line when we want to enable the catalog check for new suppliers
            if validation_step == 1:
                if self.RequestTypeId == 1 or self.RequestTypeId == 2:
                    if not is_new_supplier:
                        catalog_result = self.match_supplier_catalog(column_result["data"])
                        if not catalog_result["success"] and catalog_result.get(
                            "approval_required", False
                        ):
                            approval_reasons.append(catalog_result["reason"])
                            self.request_logger.warning(f"Approval required: {catalog_result['reason']}")
                else:
                    self.request_logger.info("Skipping catalog ID check for new supplier with no existing data for non-product related requests")

            if self.RequestTypeId == 1 or self.RequestTypeId == 4:
                validation_result = self.validate_warehouses(column_result["data"])

                # Check for specific error conditions that should fail the request
                if validation_result.get("error_message") and validation_result.get("error_message").startswith("ERROR:"):
                    error_msg = validation_result.get("error_message")
                    self.request_logger.error(error_msg)
                    self.error_log(logs=error_msg)
                    self.Error = error_msg
                    self.Status = "Canceled"
                    self.update_request_status(stage_id=2, status_id=5)
                    return {"success": False}

                # Continue with normal approval flow for other validation issues
                if not validation_result["success"] and validation_result.get(
                    "approval_required", False
                ):
                    approval_reasons.append(validation_result["reason"])
                    self.request_logger.warning(
                        f"Approval required: {validation_result['reason']}"
                    )
            else:
                self.request_logger.info("Skipping warehouse validation for non-stock related requests")

            # If approval is required, return early
            if approval_reasons:
                return {
                    "success": True,
                    "approval_required": True,
                    "reasons": approval_reasons,
                }

            # Sub-step 2.7: Process data
            if validation_step == 2:
                processing_result = self._process_data(column_result["data"])
                if not processing_result["success"]:
                    return {"success": False}

            return {"success": True}

        except Exception as e:
            self._handle_exception(e)
            return {"success": False}

    def _step_handle_approval(self, approval_reasons: List[str]) -> None:
        """
        Step 3: Handles the approval process when required.

        Args:
            approval_reasons (List[str]): Reasons requiring approval.

        Returns:
            None: Processing is paused for approval.
        """
        self.request_logger.info("Request has been routed for approval")
        self.info(logs="Request has been routed for approval")

        approval_reason_str = "; ".join(approval_reasons)

        # Create a special report for approval reasons
        self.add_request_report(
            import_type_id=self.ImportTypeId, approval_reasons=approval_reasons
        )

        # Add a log entry with the approval reasons
        self.info(logs=f"Approval required for the following reasons: {approval_reason_str}")

        # Set status to indicate approval is pending
        self.Status = "Pending"
        self.update_request_status(stage_id=7, status_id=6)

    def _step_finalize_import(self) -> None:
        """
        Step 4: Finalizes the import process by updating status and generating reports.
        """
        print("*" * 40)
        print(self.ImportTypeId)
        print("*" * 40)

        self.add_request_report(import_type_id=self.ImportTypeId)

        # If Status is already set to "Pending", don't change it
        if self.Status != "Pending":
            self.Status = "Canceled" if self.Error else "Finished"

        if self.Status == "Canceled":
            self.update_request_status(stage_id=2, status_id=5)
        elif self.Status == "Finished":
            # Normal completion - set to stage 7 (Approval) and status 6 (Pending)
            self.update_request_status(stage_id=7, status_id=6)
        # If Status is "Pending", it's already been set to the appropriate stage and status

    def _convert_to_dataframe(self) -> Optional[pd.DataFrame]:
        """
        Converts the input file to a pandas DataFrame.

        Returns:
            Optional[pd.DataFrame]: DataFrame if conversion was successful, None otherwise.
        """
        self.request_logger.info("Converting Excel File to DataFrame")
        self.update_request_status(status_id=3, stage_id=2, sub_stage=10)
        self.info(logs="Converting Excel File to DataFrame")
        return self.excel_to_df(self.ProductInputFileName)

    def _map_headers(self, file_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Maps the headers of the input file to database table headers.

        Args:
            file_df (pd.DataFrame): Input DataFrame.

        Returns:
            Dict[str, Any]: Result dictionary with success status and mapped DataFrame.
        """
        self.request_logger.info("Mapping Product Header with database Table header")
        self.update_request_status(status_id=3, stage_id=2, sub_stage=11)
        self.info(logs="Mapping Product Header with database Table header")

        # The function now returns 4 values: DataFrame, message, unmapped_product_columns, unmapped_property_columns
        header_mapper_df, msg, unmapped_product_columns, unmapped_property_columns = self.read_and_map_headers_for_product(file_df)

        print("*"*40)
        print(f"{header_mapper_df = }")
        print(f"{msg = }")
        print(f"{unmapped_product_columns = }")
        print(f"{unmapped_property_columns = }")
        print("*"*40, end="\n\n")

        # If header_mapper_df is None, there was an error (could be unmapped product columns or other error)
        if header_mapper_df is None:
            self.Error = msg
            self.Status = "Canceled"
            self.request_logger.error(f"ERROR: {msg}")
            self.error_log(logs=f"ERROR: {msg}")
            self.update_request_status(stage_id=2, status_id=5)
            return {"success": False}

        # Check if there are unmapped property columns that require approval
        if unmapped_property_columns:
            self.request_logger.warning(f"Unmapped property columns found: {unmapped_property_columns}")
            return {
                "success": True,
                "data": header_mapper_df,
                "approval_required": True,
                "reason": f"Unmapped property columns found: {', '.join(unmapped_property_columns)}",
            }

        return {"success": True, "data": header_mapper_df}

    def _remove_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Removes duplicate rows from the DataFrame.

        Args:
            df (pd.DataFrame): Input DataFrame.

        Returns:
            pd.DataFrame: DataFrame with duplicates removed.
        """
        self.request_logger.info("Removing Duplicates")
        self.update_request_status(status_id=3, stage_id=2, sub_stage=12)
        self.info(logs="Removing Duplicates")
        return self.remove_duplicate(df)

    def _map_columns(self, df: pd.DataFrame) -> Dict[str, Any]:  # change the name with validate_and_prepare_columns
        """
        Maps the columns of the DataFrame according to configuration.

        Args:
            df (pd.DataFrame): Input DataFrame.

        Returns:
            Dict[str, Any]: Result dictionary with success status and mapped DataFrame.
        """
        return self.map_columns(df)

    def _process_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Processes the data through transformations, filtering, and database insertion.

        Args:
            data (pd.DataFrame): Input DataFrame.

        Returns:
            Dict[str, Any]: Result dictionary with success status.
        """
        try:
            self.request_logger.info("Processing Data: Splitting, Filtering, and Inserting")
            self.info(logs="Processing Data: Splitting, Filtering, and Inserting")

            # Prepare data
            df = pd.DataFrame(data)
            df["SupplierId"] = self.SupplierID
            df["SupplierName"] = self.SupplierName

            # Process with cleaner
            result = cleaner.process_excel(df, self.RequestTypeId)

            # Check if processing was successful
            if not result.get("success", False):
                error_message = result.get("error", "Unknown error")
                self.Error = error_message
                self.request_logger.error(f"Error in process_excel: {error_message}")
                self.error_log(logs=f"Error in process_excel: {error_message}")
                self.Status = "Canceled"
                self.update_request_status(stage_id=2, status_id=5)
                return {"success": False}

            # Extract processed data
            products = result.get("products", pd.DataFrame())
            prices = result.get("prices", pd.DataFrame())
            stock = result.get("stock", pd.DataFrame())
            validate_df = result.get("validate_df", pd.DataFrame())

            # Filter product data
            filter_result = filter_processor.filter_product_data(prices, stock)
            valid_price_data = valid_stock_data = pd.DataFrame()

            if filter_result:
                valid_price_data = filter_result.get("valid_price_data")
                valid_stock_data = filter_result.get("valid_stock_data")
                combined_filter_errors = filter_result.get("combined_errors")

                # Update error count
                self.ErrorCount = self.get_count_of_data(
                    data_source=combined_filter_errors
                )

                # Send error report if needed
                if not combined_filter_errors.empty:
                    self.send_price_size_unit_filter_error_report(
                        error_report_df=combined_filter_errors
                    )

            # Insert data into MongoDB
            self.request_logger.info("Inserting Sheet Excel in Mongo DB")
            self.result(logs="Inserting Sheet Excel in Mongo DB")
            process_dataframes_and_insert(
                products,
                valid_price_data,
                valid_stock_data,
                self.RequestTypeId,
                self.ImportRequestId,
            )
            self.update_request_status(stage_id=2, status_id=3)

            # Handle validation report if needed
            if not validate_df.empty:
                self.ValidationCount = self.get_count_of_data(data_source=validate_df)
                self.send_cas_mdl_purity_report(validated_report_df=validate_df)

            return {"success": True}

        except Exception as e:
            self._handle_exception(e)
            return {"success": False}

    def _handle_exception(self, exception: Exception) -> None:
        """
        Handles exceptions that occur during the import process.

        Args:
            exception (Exception): The exception that occurred.
        """
        self.request_logger.error(
            f"[[ImportRequestID]] => {exception}", exc_info=True
        )
        self.error_log(logs=f"[[ImportRequestID]] => {exception}")
        self.update_request_status(stage_id=2, status_id=5)
        self.Status = "Canceled"
        self.Error = str(exception)
