from server import config, ISPRODUCTION, warnings, logger, date, wget, requests, time
from server.com.handler.LoggerHandler import <PERSON>gger<PERSON>andler

log = LoggerHandler()
warnings.filterwarnings('ignore')


class FileManagerApi:
    def __init__(self):
        self.__filemanager_base_url = config.get("ProductionApi", "FileManagerBaseUrl") if ISPRODUCTION else config.get(
            "TestingAPI", "FileManagerBaseUrl")
        self.__existfile_endpoint = self.__filemanager_base_url + 'File/FileExists'
        self.__getfile_endpoint = self.__filemanager_base_url + 'api/external/file/downloadFile'
        self.__uploadfile_endpoint = self.__filemanager_base_url + 'api/external/file/uploadFile/'

    # def get_file(self, endpoint: str, params: dict):
    #     try:
    #         res = requests.get(endpoint, params=params, verify=False)
    #         return res
    #     except Exception as e:
    #         log.request_logger.error(e, exc_info=True)
    #         return
    #
    # def file_exist(self, file: str, tag: str):
    #     try:
    #         params = {"strFileName": file, "moduleType": tag}
    #         return self.get_file(self.__existfile_endpoint, params)
    #     except Exception as e:
    #         log.request_logger.error(e, exc_info=True)
    #         return

    def download_file_(self, endpoint: str, bucket_id: str, file_uuid: str, out_path: str):
        try:
            url = f"{endpoint}?bucket_uuid={bucket_id}&file_uuid_name={file_uuid}"
            print(url)
            res = wget.download(url, out=out_path)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def download_file(self, bucket_id: str, file_uuid: str, out_path: str):
        try:
            res = self.download_file_(self.__getfile_endpoint, bucket_id=bucket_id, file_uuid=file_uuid,
                                      out_path=out_path)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def post_file(self, endpoint: str, payload: dict, files: dict):
        try:
            res = requests.post(endpoint, params=payload, files=files, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def upload_file(self, filename: str, file: object, file_path: str):
        bucket_uuid = config.get("Data", "BUCKET_UUID")
        attempt = 0
        max_attempts = 3
        while attempt < max_attempts:
            try:
                files = {"file": (filename, file, "text/csv")}
                payload = {"bucket_uuid": bucket_uuid, "file_path": file_path}
                res = self.post_file(self.__uploadfile_endpoint, payload=payload, files=files)

                if res and res.status_code == 200:
                    return res
            except Exception as e:
                log.request_logger.error(e, exc_info=True)

            attempt += 1
            time.sleep(5)  # Wait for 5 seconds before retrying

        log.request_logger.error(f"Failed to upload file after {max_attempts} attempts.")
        return None


class ApiHandler(FileManagerApi):
    def __init__(self):
        super().__init__()
        self.__middleware_base_url = config.get("ProductionApi", "MiddlewareBaseUrl") if ISPRODUCTION else config.get(
            "TestingAPI", "MiddlewareBaseUrl")
        self.__update_request_endpoint = self.__middleware_base_url + "api/updateRequestStatus"
        self.__get_open_product_chemindex_endpoint = self.__middleware_base_url + "api/getRemainingFile"
        self.__add_request_report_endpoint = self.__middleware_base_url + 'api/addRequestReport'
        self.__addmiddleware_log_endpoint = self.__middleware_base_url + "api/addLogs"
        self.__add_output_file_endpoint = self.__middleware_base_url + 'api/addOutputFile'

    def post(self, endpoint: str, payload: dict):

        try:
            res = requests.put(endpoint, json=payload, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def log_post(self, endpoint: str, payload: dict):

        try:
            res = requests.post(endpoint, json=payload, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def get(self, endpoint: str):

        try:
            res = requests.get(endpoint, verify=False)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def get_open_product_import_request(self):
        try:
            res = self.get(endpoint=self.__get_open_product_chemindex_endpoint)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_output_file(self, endpoint: str, import_id: int, output_file: str, file_uuid_name: str, import_type_id: int,
                        output_file_type_id: int) -> None:
        try:
            data = {"ImportRequestID": import_id, "FileName": output_file, "OutputFileTypeId": output_file_type_id,
                    "ImportTypeId": import_type_id,
                    "FileUUID": file_uuid_name}
            print("OutputProductImport", data)
            res = self.log_post(endpoint, payload=data)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_import_filename(self, output_file: str, file_uuid: str, import_id: int, import_type_id: int,
                            output_file_type_id: int):
        try:
            return self.add_output_file(
                endpoint=self.__add_output_file_endpoint, output_file=output_file, file_uuid_name=file_uuid,
                import_id=import_id, import_type_id=import_type_id,
                output_file_type_id=output_file_type_id)
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def update_request(self, status_id: int, stage_id: int, import_id: int, request_time, sub_stage_id: int, process_request_time: dict):
        try:
            payload = {"StatusId": status_id, "StageId": stage_id, "ImportRequestID": import_id}
            if request_time is not None:
                payload["RequestTime"] = request_time
            if sub_stage_id is not None:
                payload["SubStageId"] = sub_stage_id
            if process_request_time != {}:
                payload["ProcessRequestTime"] = process_request_time

            resp = self.post(self.__update_request_endpoint, payload=payload)
            print(resp.json())
            return resp
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_log(self, import_id: int, com_name: str, logs: str, log_type: str):
        try:
            data = {"ImportRequestID": import_id, "ComponentName": com_name, "LogType": log_type, "Logs": logs}
            res = self.log_post(endpoint=self.__addmiddleware_log_endpoint, payload=data)
            return
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return

    def add_report(self, import_id: int, import_type_id: int, input_count: int, new_count: int, exists_count: int,
                   error_count: int, update_count: int,
                   duplicate_count: int):
        try:
            data = {"ImportRequestID": import_id, "ImportTypeId": import_type_id, "InputDataCount": input_count,
                    "NewDataCount": new_count,
                    "ExistsDataCount": exists_count, "ErrorDataCount": error_count, "UpdateDataCount": update_count,
                    "DuplicateDataCount": duplicate_count}
            res = self.log_post(endpoint=self.__add_request_report_endpoint, payload=data)
            return res
        except Exception as e:
            log.request_logger.error(e, exc_info=True)
            return
