from server import config, pd, logger
import pyodbc
from pymongo import MongoClient
from server.com.handler.LoggerHandler import LoggerHandler

log = LoggerHandler()

URL = config.get('MongoDB', 'URL')
DATABASE_NAME = config.get('MongoDB', 'DATABASE_NAME')

Server = config.get('CREDENTIAL', 'SERVER')
Username = config.get('CREDENTIAL', 'USERNAME')
Password = config.get('CREDENTIAL', 'PASSWORD')
Database = config.get('CREDENTIAL', 'DATABASE')


def create_connection_with_ssms():
    try:
        conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={Server};Database={Database};uid={Username};pwd={Password};"
        print(conn_string)
        conn = pyodbc.connect(conn_string)
    except (Exception,):
        conn_string = (
            f"DRIVER={{ODBC Driver 18 for SQL Server}};"
            f"SERVER={Server};"
            f"DATABASE={Database};"
            f"UID={Username};"
            f"PWD={Password};"
            "Encrypt=yes;TrustServerCertificate=yes;"
        )
        conn = pyodbc.connect(conn_string)
    return conn


def db_connection(request_type):
    """Establish a connection to MongoDB and return only the required collections based on request_type."""
    log.request_logger.info("Creating connection with Mongodb")
    client = MongoClient(URL)
    db = client[DATABASE_NAME]

    collections = {}
    if request_type == 1 or request_type == 2:
        collections['ProductExcel'] = db['ProductExcel']
    if request_type == 1 or request_type == 3:
        collections['PriceExcel'] = db['PriceExcel']
    if request_type == 1 or request_type == 4:
        collections['StockExcel'] = db['StockExcel']

    return client, db, collections


def handle_missing_values(df):
    """Handle missing values by replacing NAType with None for MongoDB compatibility."""
    return df.where(pd.notna(df), None)


def insert_data_into_mongodb(df, collection):
    """Insert data from DataFrame into MongoDB collection."""
    if not df.empty:

        # NOTE: This line drops the entire MongoDB collection, including all documents and indexes.
        # TODO: Change the logic — do NOT insert data into MongoDB during primary validation.
        #       Data should only be inserted after successful secondary validation.
        #       For now, we are dropping the collection to avoid duplicate data.

        if (doc_count := collection.estimated_document_count()) > 0:
            log.request_logger.warning(
                f"Collection '{collection.name}' exists and contains {doc_count} documents. "
                f"Dropping the collection to avoid duplicate entries."
            )
            collection.drop()
        else:
            log.request_logger.info(
                f"Collection '{collection.name}' is empty or does not exist. Proceeding to insert data."
            )

        df_cleaned = handle_missing_values(df)
        data_to_insert = df_cleaned.to_dict(orient='records')
        result = collection.insert_many(data_to_insert)
        log.request_logger.info(f"Inserted {len(data_to_insert)} rows into MongoDB collection {collection.name}.")
        return result.inserted_ids


def process_dataframes_and_insert(products, prices, stocks, request_type, req_id):
    log.request_logger.info("Inserting Data into Mongo DB")

    client, db, collections = db_connection(request_type)

    if 'ProductExcel' in collections:
        if not products.empty:
            collection_name = f"{req_id}_ProductExcel"
            collection = db[collection_name]  # Get the collection object from the db
            logger.info(f"Inserting Data {collection_name} into Mongo DB")
            insert_data_into_mongodb(products, collection)

    if 'PriceExcel' in collections:
        if not prices.empty:
            collection_name = f"{req_id}_PriceExcel"
            collection = db[collection_name]  # Get the collection object from the db
            logger.info(f"Inserting Data {collection_name} into Mongo DB")
            insert_data_into_mongodb(prices, collection)

    if 'StockExcel' in collections:
        if not stocks.empty:
            collection_name = f"{req_id}_StockExcel"
            collection = db[collection_name]  # Get the collection object from the db
            logger.info(f"Inserting Data {collection_name} into Mongo DB")
            insert_data_into_mongodb(stocks, collection)

    client.close()
    return

