[Data]
ISPRODUCTION = # ISPRODUCTION
RABBITMQ = # RABBITMQ connection string
BUCKET_UUID = # BUCKET_UUID
UPLOAD_FILE_PATH = # UPLOAD_FILE_PATH

[PORTS]
PRODUCT_WORKER_PORT = # PORT
PRICE_WORKER_PORT = # PORT
STOCK_WORKER_PORT = # PORT
UPLOAD_WORKER_PORT = # PORT

[TestingAPI]
MiddlewareBaseUrl = # Testing Api Baseurl
FileManagerBaseUrl = # Testing Api cloud doc Api

[ProductionApi]
MiddlewareBaseUrl = # Live Api Baseurl
FileManagerBaseUrl = # Live Api cloud doc Api

[PATH]
MainTempFolder = Temp
LogFilesFolder = Log
QueueLogFilesFolder = Log/QueueLog
RequestLogFilesFolder = Log/RequestLog
ScriptsFolder = Scripts
ScriptsLogFolder = Scripts/Log
TempProductImport = Temp/ProductImport
TempPriceImport = Temp/PriceImport
TempStockImport = Temp/StockImport

[Scripts]
[ProductWorkerScripts]
AllowedScript = [
                    {"TypeId": 2, "ScriptName": "ProductImport"}
                ]

[PriceWorkerScripts]
AllowedScript = [
                    {"TypeId": 3, "ScriptName": "PriceImport"}
                ]

[StockWorkerScripts]
AllowedScript = [
                    {"TypeId": 4, "ScriptName": "StockImport"}
                ]

[FileType]
1 = InputProductFile
2 = ExistsProductFile
3 = NewProductFile
4 = ErrorProductFile
5 = UpdateProductFile

[ProductData]
ProductAllowFileType = {"input_data_count": {"fileType":1, "varName":"InputDataCount"},
                        "exists_data_count": {"fileType":2, "varName":"ExistsDataCount"},
                        "new_data_count": {"fileType":3, "varName":"NewDataCount"},
                        "error_data_count": {"fileType":4, "varName":"ErrorDataCount"},
                        "update_data_count": {"fileType":5, "varName":"UpdateDataCount"}}

[MongoDB]
URL = # Mongodb connection URL
TEMP_DATA_MONGO_DBNAME = # Temp Dataimporting database name
REQUEST_MONGO_DBNAME = # Request info database name 

[MONGO_CREDENTIAL]
URL = # Mongodb connection URL

[ChemIndex_CREDENTIAL]
SERVER = # server ip
USERNAME = # server username
PASSWORD = # server password
DATABASE = # server databasename

[CREDENTIAL]
SERVER = # server ip
USERNAME = # server username
PASSWORD = # server password
DATABASE = # server databasename