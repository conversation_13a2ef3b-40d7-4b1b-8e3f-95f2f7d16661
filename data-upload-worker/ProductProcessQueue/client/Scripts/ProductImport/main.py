import sys
import math
import pandas as pd
import numpy as np
from rdkit import Chem, RDLogger
from rdkit.Chem import inchi
import pyodbc
import warnings
from loggerfile import logger
from datetime import timezone, datetime

from config_manager import ConfigManager
from chemindex import ProductImporting
from mongodb import read_data_from_mongo, insert_data_into_mongodb

RDLogger.DisableLog('rdApp.*')
chemindex = ProductImporting()


class DataUpload:
    warnings.filterwarnings("ignore")

    def __init__(self, server, database, username, password, chem_server, chem_db, request_mongo_dbname):
        warnings.filterwarnings("ignore")
        self.initialize_request()
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.chem_server = chem_server
        self.chem_db = chem_db
        self.request_mongo_dbname = request_mongo_dbname
        self.temp_data_mongo_dbname = sys.argv[1]

        self.Req_id = sys.argv[4]
        self.SelectedDate = sys.argv[5]
        self.product_df = pd.DataFrame()
        self.match_product_df = pd.DataFrame()
        self.conn = None
        self.chem_conn = None
        self.cat_match_in_spd = pd.DataFrame()
        self.match_cat_in_spd_df = pd.DataFrame()
        self.non_match_cat_in_df = pd.DataFrame()
        self.exists_cat_df = pd.DataFrame()
        self.new_cat_df = pd.DataFrame()
        self.df2 = pd.DataFrame()
        self.merged_df = pd.DataFrame()
        self.excel_df = pd.DataFrame()
        self.mp_df = pd.DataFrame()
        self.exists_smile_in_mp_df = pd.DataFrame()
        self.not_exists_smile_in_mp_df = pd.DataFrame()
        self.exists_chem_product_in_mp = pd.DataFrame()
        self.non_exists_chem_product_in_mp = pd.DataFrame()
        self.exists_inchi_in_chem = pd.DataFrame()
        self.not_exists_inchi_in_chem = pd.DataFrame()
        self.chemical_property = pd.DataFrame()
        self.chem_product_df = pd.DataFrame()
        self.active_property_names = []

    def initialize_request(self):
        self.temp_data_mongo_dbname = sys.argv[1]
        self.Req_id = sys.argv[4]
        self.SelectedDate = sys.argv[5]
        self.product_df = pd.DataFrame()
        self.match_product_df = pd.DataFrame()
        self.conn = None
        self.chem_conn = None
        self.cat_match_in_spd = pd.DataFrame()
        self.match_cat_in_spd_df = pd.DataFrame()
        self.non_match_cat_in_df = pd.DataFrame()
        self.exists_cat_df = pd.DataFrame()
        self.new_cat_df = pd.DataFrame()
        self.df2 = pd.DataFrame()
        self.merged_df = pd.DataFrame()
        self.excel_df = pd.DataFrame()
        self.mp_df = pd.DataFrame()
        self.exists_smile_in_mp_df = pd.DataFrame()
        self.not_exists_smile_in_mp_df = pd.DataFrame()
        self.exists_chem_product_in_mp = pd.DataFrame()
        self.non_exists_chem_product_in_mp = pd.DataFrame()
        self.exists_inchi_in_chem = pd.DataFrame()
        self.not_exists_inchi_in_chem = pd.DataFrame()
        self.chemical_property = pd.DataFrame()
        self.chem_product_df = pd.DataFrame()
        self.active_property_names = []

    '''Making Connection with Main Database'''
    def connect_to_database(self):
        try:
            conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)
        logger.info("Connected to the database.")

    '''Making Connection with ChemIndex Database'''
    def chem_index_connect_to_database(self):
        try:
            conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.chem_server};Database={self.chem_db};uid={self.username};pwd={self.password};"
            conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.chem_server};"
                f"DATABASE={self.chem_db};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            conn = pyodbc.connect(conn_string)
        logger.info("Connected to the database.")
        return conn

    '''Reading Data of ChemProduct table and getting into dataframe'''
    def read_data_from_chem_product_database(self):
        conn = self.chem_index_connect_to_database()
        query = """select ChemProductID as id, MolFile as smile, Can_Smile as can_smiles, InChiKey from [dbo].[ChemProducts]"""
        self.chem_product_df = pd.read_sql(query, conn)
        conn.close()
        print("Read data from the ChemProduct database.")
        return

    '''Reading Data of ProductProperties table and getting into dataframe'''
    def read_data_from_chemical_property(self):
        logger.info("read_data_from_chemical_property")
        self.connect_to_database()
        query = 'SELECT PropertyNameId, PropertyName FROM [dbo].[ProductProperties]'
        self.chemical_property = pd.read_sql(query, self.conn)
        self.conn.close()

    '''Reading Data of SupplierProductProperties table and getting into dataframe'''
    def read_data_from_properties_value(self, df):
        logger.info("read_data_from_properties_value")
        self.connect_to_database()

        product_ids = df['SupplierProductId'].tolist()

        batch_size = 1000
        map_chemical_value = pd.DataFrame()  # Initialize an empty DataFrame

        for i in range(0, len(product_ids), batch_size):
            product_ids_batch = product_ids[i:i + batch_size]
            product_ids_str = ", ".join(str(id) for id in product_ids_batch)
            query = f"""
                    SELECT * FROM [dbo].[SupplierProductProperties]
                    WHERE SupplierProductId IN ({product_ids_str})
                """
            batch_df = pd.read_sql(query, self.conn)
            map_chemical_value = map_chemical_value.append(batch_df, ignore_index=True)
        self.conn.close()
        return map_chemical_value

    '''Reading Data of SupplierProducts table and getting into dataframe'''
    def read_data_from_supplier_product(self):
        logger.info("read_data_from_supplier_product")
        self.connect_to_database()

        catalog_ids = self.product_df['SupplierCatalogId'].tolist()
        supplier_index = self.product_df['SupplierId'].first_valid_index()
        supplier_id = self.product_df.at[supplier_index, 'SupplierId'] if supplier_index is not None else None

        batch_size = 1000
        batches = math.ceil(len(catalog_ids) / batch_size)

        self.cat_match_in_spd = pd.DataFrame()

        for i in range(batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(catalog_ids))
            catalog_ids_batch = catalog_ids[start_idx:end_idx]

            catalog_ids_str = ", ".join(f"'{str(id)}'" for id in catalog_ids_batch)
            query = f"""SELECT * FROM [dbo].[SupplierProducts]
                        WHERE SupplierCatalogId IN ({catalog_ids_str})
                        and OmsEchemPortalSupplierId = {supplier_id}"""

            batch_result = pd.read_sql(query, self.conn)
            self.cat_match_in_spd = pd.concat([self.cat_match_in_spd, batch_result], ignore_index=True)

        exists_product = self.product_df.merge(
            self.cat_match_in_spd[['SupplierCatalogId', 'SupplierProductId']],
            on='SupplierCatalogId',
            how='inner'
        )
        insert_data_into_mongodb(self.temp_data_mongo_dbname, exists_product, f'{self.Req_id}_ExistsSupplierProduct')

        # self.match_product_df = self.product_df[
        #     self.product_df['SupplierCatalogId'].isin(self.cat_match_in_spd['SupplierCatalogId'])
        # ]
        self.match_product_df = self.product_df.merge(
            self.cat_match_in_spd[['SupplierCatalogId', 'LastUpdatedAt']],
            on='SupplierCatalogId',
            how='inner'
        )

        self.non_match_cat_in_df = self.product_df[
            ~self.product_df['SupplierCatalogId'].isin(self.cat_match_in_spd['SupplierCatalogId'])
        ]

        logger.info(f"Total records processed: {len(self.cat_match_in_spd)}")
        logger.info(f"Total matched records: {len(self.match_product_df)}")
        logger.info(f"Total non-matched records: {len(self.non_match_cat_in_df)}")

        self.conn.close()

    '''Reading Data of MainProducts table and getting into dataframe'''
    def read_data_from_main_product(self):
        logger.info("read_data_from_main_product")
        self.connect_to_database()
        query = """select * from MainProducts"""
        self.mp_df = pd.read_sql(query, self.conn)
        self.conn.close()

    '''Reading Data of ProductProperties table and getting into dataframe'''
    def read_data_from_chemical_properties_name(self):
        logger.info("read_data_from_chemical_properties_name")
        self.connect_to_database()
        query = """SELECT PropertyName FROM ProductProperties WHERE IsActive = 1"""

        cursor = self.conn.cursor()
        cursor.execute(query)

        self.active_property_names = [row[0] for row in cursor.fetchall()]
        self.conn.close()

    '''Converting Smile into inChiKey'''
    def smiles_to_inchikey(self, smiles):
        try:
            molecule = Chem.MolFromSmiles(smiles)
            if molecule:
                inchi_str = inchi.MolToInchi(molecule)
                if inchi_str:
                    return inchi.InchiToInchiKey(inchi_str)
        except Exception as e:
            print(f"Error processing SMILES '{smiles}': {e}")
        return np.nan

    '''Checking Smile is in dataframe are empty or not'''
    def check_smile(self, df):
        logger.info("Checking Smile in sheet")
        have_smile_df = df[df['Smile'].notna() & (df['Smile'] != '')]
        not_smile_df = df[df['Smile'].isna() | (df['Smile'] == '')]
        return have_smile_df, not_smile_df

    '''Mapping CI_ChemProductId is in MainProducts Table'''
    def map_chem_id_with_mp_table(self, exists_smile_in_chem):
        logger.info("map_chem_id_with_mp_table")
        column_name_mapping = {
            'DB_ID': 'CI_ChemProductId',
        }
        exists_smile_in_chem = exists_smile_in_chem.rename(columns=column_name_mapping)
        self.read_data_from_main_product()
        self.exists_chem_product_in_mp = exists_smile_in_chem.merge(self.mp_df[['CI_ChemProductId', 'MainProductId']],
                                                                    on='CI_ChemProductId', how='inner')
        self.non_exists_chem_product_in_mp = exists_smile_in_chem[
            ~exists_smile_in_chem['CI_ChemProductId'].isin(self.mp_df['CI_ChemProductId'])]

    '''Mapping Echem inChiKey in ChemIndex inChiKey Table'''
    def map_inchi_with_chemindex_table(self, df):
        logger.info("map_inchi_with_chemindex_table")
        self.read_data_from_chem_product_database()
        column_name_mapping = {
            'smile': 'Smile',
            'id': 'CI_ChemProductId',
            'can_smiles': 'can_smies',
            'InChiKey': 'InChiKey'
        }
        chem_index_df = self.chem_product_df.rename(columns=column_name_mapping)
        chem_product_inchi_dict = dict(zip(chem_index_df['InChiKey'], chem_index_df['CI_ChemProductId']))
        temp_inchi_match_in_chem = pd.DataFrame(df)
        temp_inchi_match_in_chem['DB_ID'] = None
        for index, row in df.iterrows():
            inchi = row['InChiKey']
            if inchi in chem_product_inchi_dict:
                temp_inchi_match_in_chem.at[index, 'DB_ID'] = chem_product_inchi_dict[inchi]

        self.exists_inchi_in_chem = temp_inchi_match_in_chem[temp_inchi_match_in_chem['DB_ID'].notna()]
        self.not_exists_inchi_in_chem = temp_inchi_match_in_chem[temp_inchi_match_in_chem['DB_ID'].isna()]
        return

    '''Finding Difference of Product Details change with old details'''
    def find_differences(self, existing_df, new_df, audit_only=False):
        logger.info("Finding Difference of Product Details change with old details")

        new_df = new_df.replace({None: np.nan})
        existing_df = existing_df.replace({None: np.nan})

        # Columns to check for differences
        check_columns = ['ProductName', 'CASNumber', 'MDLNumber', 'Url', 'Purity', 'MainProductId', 'Smile']

        # Merge the two dataframes on 'SupplierCatalogID'
        merged_df = pd.merge(existing_df, new_df, on='SupplierCatalogId', suffixes=('_old', '_new'))

        if audit_only:
            product_audit_changes = []
            for _, row in merged_df.iterrows():
                for col in check_columns:
                    product_audit_changes.append({
                        'SupplierProductId': row['SupplierProductId'],
                        'ColumnName': col,
                        'Value': row[f"{col}_new"],
                        'CreatedAt': pd.to_datetime(self.SelectedDate)
                    })
            product_audit_df = pd.DataFrame(product_audit_changes,
                                            columns=['SupplierProductId', 'ColumnName', 'Value', 'CreatedAt'])
            return None, product_audit_df

        # List to hold rows with changes
        changes = []
        product_audit_changes = []

        # Iterate through merged rows
        for _, row in merged_df.iterrows():
            # Track changes for the current row
            row_changes = {'SupplierCatalogId': row['SupplierCatalogId']}  # Always include SupplierCatalogID
            has_changes = False  # Flag to track if there are any changes in the row

            # Iterate through columns to check for differences
            for col in check_columns:
                old_value = row[f"{col}_old"]
                new_value = row[f"{col}_new"]

                # Check if values are different, accounting for NaN
                if pd.isna(old_value) and pd.isna(new_value):
                    row_changes[col] = old_value  # Both are NaN, treat as unchanged
                elif old_value != new_value:
                    row_changes[col] = new_value  # Store the new value
                    has_changes = True  # Mark as changed

                    # Record the change in product_audit_changes using SupplierProductID from existing_df
                    product_audit_changes.append({
                        'SupplierProductId': row['SupplierProductId'],
                        'ColumnName': col,
                        'Value': new_value if audit_only else old_value,
                        'CreatedAt': pd.to_datetime(self.SelectedDate) if audit_only else row['LastUpdatedAt_old']
                    })
                else:
                    row_changes[col] = old_value  # Keep the old value

            # If there are any changes, include additional columns
            if has_changes:
                row_changes['SupplierName'] = row['SupplierName']
                row_changes['SupplierId'] = row['SupplierId']
                row_changes['Smile'] = row['Smile_new']
                row_changes['MainProductId'] = row['MainProductId_new']
                changes.append(row_changes)

        # Create the resulting dataframe for summary of changes (other_df)
        other_df = pd.DataFrame(changes, columns=['SupplierCatalogId', 'ProductName', 'CASNumber', 'MDLNumber',
                                                  'SupplierId', 'SupplierName', 'Smile', 'Url', 'Purity',
                                                  'MainProductId'])

        # Create the product_audit_df for detailed change tracking
        product_audit_df = pd.DataFrame(product_audit_changes, columns=['SupplierProductId', 'ColumnName', 'Value', 'CreatedAt'])

        return other_df, product_audit_df

    '''Processing chemical properties data'''
    def process_chemical_properties(self, user_df):
        logger.info("Processing chemical properties data")

        # Extract columns of interest
        if not self.active_property_names:
            self.read_data_from_chemical_properties_name()
        columns_to_check = self.active_property_names

        # Filter only those columns which are present in the DataFrame
        available_columns = [col for col in columns_to_check if col in user_df.columns]

        # Fetch all existing records from SupplierChemicalProperties
        existing_data = self.read_data_from_properties_value(user_df)

        # Initialize empty DataFrames
        update_property_df = pd.DataFrame(columns=['PropertyNameId', 'SupplierProductId', 'PropertyValue', 'SupplierId'])
        new_chemical_property_df = pd.DataFrame(columns=['PropertyNameId', 'SupplierProductId', 'PropertyValue', 'SupplierId'])
        product_properties_audits_df = pd.DataFrame(columns=['SupplierProductId', 'PropertyNameId', 'PropertyValue'])
        disable_df = pd.DataFrame(columns=['SupplierProductId', 'PropertyNameId', 'IsDisable', 'SupplierId', 'PropertyValue'])

        # Iterate through each row in user data
        for _, row in user_df.iterrows():
            for prop in available_columns:
                # Get the corresponding PropertyNameId
                property_name_id = self.chemical_property[self.chemical_property['PropertyName'] == prop][
                    'PropertyNameId'].values
                if property_name_id.size > 0:
                    property_name_id = property_name_id[0]

                    if pd.notna(row[prop]):
                        existing_row = existing_data[
                            (existing_data['SupplierProductId'] == row['SupplierProductId']) &
                            (existing_data['PropertyNameId'] == property_name_id)
                            ]

                        if not existing_row.empty:
                            current_value = existing_row.iloc[0]['PropertyValue']
                            is_disabled = existing_row.iloc[0].get('IsDisable', 0)

                            if current_value != row[prop]:
                                update_property_df = update_property_df.append({
                                    'SupplierCatalogId': row['SupplierCatalogId'],
                                    'PropertyNameId': property_name_id,
                                    'SupplierProductId': row['SupplierProductId'],
                                    'PropertyValue': row[prop],
                                    'SupplierId': row['SupplierId']
                                }, ignore_index=True)

                                product_properties_audits_df = product_properties_audits_df.append({
                                    'SupplierProductId': row['SupplierProductId'],
                                    'PropertyNameId': property_name_id,
                                    'PropertyValue': current_value,
                                    'SupplierId': row['SupplierId']
                                }, ignore_index=True)

                            # ✅ Re-enable if previously disabled
                            if is_disabled == 1:
                                current_value = existing_row.iloc[0].get('PropertyValue')  # 🟡 Fetch the latest value
                                disable_df = disable_df.append({
                                    'SupplierProductId': row['SupplierProductId'],
                                    'PropertyNameId': property_name_id,
                                    'IsDisable': 0,
                                    'SupplierId': row['SupplierId'],
                                    'PropertyValue': current_value  # ✅ Include it here
                                }, ignore_index=True)

                        else:
                            # Insert new property
                            new_chemical_property_df = new_chemical_property_df.append({
                                'SupplierCatalogId': row['SupplierCatalogId'],
                                'PropertyNameId': property_name_id,
                                'SupplierProductId': row['SupplierProductId'],
                                'PropertyValue': row[prop],
                                'SupplierId': row['SupplierId']
                            }, ignore_index=True)

                    else:
                        # Only disable if the property exists in the table
                        existing_row = existing_data[
                            (existing_data['SupplierProductId'] == row['SupplierProductId']) &
                            (existing_data['PropertyNameId'] == property_name_id)
                            ]

                        if not existing_row.empty:
                            current_value = existing_row.iloc[0].get('PropertyValue')  # 🟡 Fetch existing value
                            disable_df = disable_df.append({
                                'SupplierProductId': row['SupplierProductId'],
                                'PropertyNameId': property_name_id,
                                'IsDisable': 1,
                                'SupplierId': row['SupplierId'],
                                'PropertyValue': current_value  # ✅ Include it here
                            }, ignore_index=True)

        # Return the DataFrames for new entries and updates
        return new_chemical_property_df, update_property_df, product_properties_audits_df, disable_df

    '''Inserting chemical properties into SupplierChemicalProperties dataframe'''
    def insert_chemical_properties(self, user_df):
        logger.info("Inserting chemical properties into SupplierProductProperties dataframe")

        # Columns to check for values
        if not self.active_property_names:
            self.read_data_from_chemical_properties_name()
        columns_to_check = self.active_property_names

        # Filter only those columns which are present in the DataFrame
        available_columns = [col for col in columns_to_check if col in user_df.columns]

        new_chemical_properties = []

        # Iterate through each row in user data
        for _, row in user_df.iterrows():
            # For each property, check if it's not NaN or None
            for prop in available_columns:
                if pd.notna(row[prop]):
                    # Get the corresponding PropertyNameId from self.chemical_property (master table)
                    property_name_id = self.chemical_property[self.chemical_property['PropertyName'] == prop][
                        'PropertyNameId'].values
                    if property_name_id.size > 0:
                        property_name_id = property_name_id[0]

                        # Create a new row for the SupplierChemicalProperties table
                        new_row = {
                            'SupplierCatalogId': row['SupplierCatalogId'],
                            'PropertyNameId': property_name_id,
                            'PropertyValue': row[prop],
                            'SupplierId': row['SupplierId']
                        }
                        new_chemical_properties.append(new_row)

        # Convert the list of new rows to a DataFrame
        new_chemical_property_df = pd.DataFrame(new_chemical_properties)

        return new_chemical_property_df

    def check_existing_report(self, request_id, supplier_id):
        """
        Check if a report exists for the given request_id and supplier_id.
        Returns the existing document if found, else None.
        """
        logger.info(f"Checking for existing report with request_id: {request_id}, supplier_id: {supplier_id}")
        try:
            existing_report = read_data_from_mongo(self.request_mongo_dbname, 'request_report')
            if not existing_report.empty:
                existing_report = existing_report[
                    (existing_report['request_id'] == int(request_id)) &
                    (existing_report['supplier_id'] == int(supplier_id))
                ]
                if not existing_report.empty:
                    return existing_report.iloc[0].to_dict()
            return None
        except Exception as e:
            logger.error(f"Error checking existing report: {e}")
            return None

    def generate_report_json(self, update_product_df, new_product, error_product_count):
        """
        Generate the JSON report in the specified format.
        """
        logger.info("Generating report JSON")

        # Calculate metrics
        old_product_num = len(self.match_product_df)  # Existing products
        new_product_num = len(self.non_match_cat_in_df)  # New products

        print('\n')
        print("+" * 50, "Update Product DataFrame:", "+" * 50) 
        print(update_product_df.columns)
        print('\n')
        print("+" * 50, "New Product DataFrame:", "+" * 50) 
        print(new_product.columns)
        print('\n') 
        print("+" * 50, "Match Product DataFrame:", "+" * 50) 
        print(self.match_product_df.columns)
        print('\n')
        print("+" * 50, "Non-Match Product DataFrame:", "+" * 50) 
        print(self.non_match_cat_in_df.columns)
        print('\n')
        print("+" * 50)

        # input("Press Enter to continue...")

        # old_product_num_with_main_product_id = len(self.match_product_df[self.match_product_df['MainProductId'].notna()])
        old_product_num_with_main_product_id = 999
        # new_product_num_with_main_product_id = len(new_product[new_product['MainProductId'].notna()])
        new_product_num_with_main_product_id = 999
        total_new_product = len(new_product)
        total_new_product_with_main_product_id = new_product_num_with_main_product_id
        inserted = len(new_product)
        updated = len(update_product_df)
        disabled = len(read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_SupplierChemicalPropertiesUpdateTemp'))
        enabled = disabled  # Assuming enabled is same as disabled for simplicity; adjust if needed

        # Get supplier information
        supplier_index = self.product_df['SupplierId'].first_valid_index()
        supplier_id = self.product_df.at[supplier_index, 'SupplierId'] if supplier_index is not None else 1
        supplier_name = self.product_df.at[supplier_index, 'SupplierName'] if supplier_index is not None else "Test Supplier"
        
        # Convert SelectedDate to ISO format
        selected_date = pd.to_datetime(self.SelectedDate).tz_localize(None).tz_localize(timezone.utc)
        timestamp = selected_date.isoformat()

        # Construct JSON
        report_json = {
            # "request_group_id": int(self.Req_id),  # Assuming request_group_id is same as Req_id
            "request_id": int(self.Req_id),
            "supplier_id": int(supplier_id),
            "supplier_name": supplier_name,
            "product": {
                "old_product_num": old_product_num,
                "new_product_num": new_product_num,
                "old_product_num_with_main_product_id": old_product_num_with_main_product_id or 0,
                "new_product_num_with_main_product_id": new_product_num_with_main_product_id or 0,
                "total_new_product": total_new_product,
                "total_new_product_with_main_product_id": total_new_product_with_main_product_id,
                "inserted": inserted,
                "updated": updated,
                "disabled": disabled,
                "enabled": enabled,
                "last_updated": datetime.now(timezone.utc).isoformat()
            },
            "timestamp": timestamp
        }

        return report_json

    def process_data(self):
        self.initialize_request()
        self.product_df = read_data_from_mongo(self.temp_data_mongo_dbname, sys.argv[2])
        logger.info(f"Input_Data_Count: {len(self.product_df)}")
        self.product_df.to_csv(f"{sys.argv[3]}/{self.Req_id}_Input_Data_Count.csv", index=False)

        if not self.product_df.empty:
            self.read_data_from_chemical_property()
            self.read_data_from_supplier_product()

            '''Exists Catalog Product'''
            # Check Exists Catalog Smile is in product Sheet
            if not self.match_product_df.empty:
                self.match_product_df.to_csv(f'{sys.argv[3]}/{self.Req_id}_Exists_Data_Count.csv', index=False)

                selected_date_dt = pd.to_datetime(self.SelectedDate)
                # Ensure LastUpdatedAt is datetime
                self.match_product_df['LastUpdatedAt'] = pd.to_datetime(self.match_product_df['LastUpdatedAt'])

                # Add a flag column: True if update should happen
                self.match_product_df['ShouldUpdate'] = selected_date_dt > self.match_product_df['LastUpdatedAt']

                # Separate into rows that should be updated vs only audited
                to_update_df = self.match_product_df[self.match_product_df['ShouldUpdate'] == True]
                only_audit_df = self.match_product_df[self.match_product_df['ShouldUpdate'] == False]

                if not to_update_df.empty:

                    exists_have_smile_df, exists_not_smile_df = self.check_smile(to_update_df)

                    logger.info("Finding difference of not smile in df")
                    if not exists_not_smile_df.empty:
                        exists_non_smile_spd_df = self.cat_match_in_spd[self.cat_match_in_spd['SupplierCatalogId'].isin(exists_not_smile_df['SupplierCatalogId'])]
                        if 'MainProductId' not in exists_not_smile_df.columns:
                            exists_not_smile_df['MainProductId'] = None
                        update_product_df, product_audit_df = self.find_differences(exists_non_smile_spd_df, exists_not_smile_df)

                        if not product_audit_df.empty:
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, product_audit_df, f'{self.Req_id}_ProductAuditInsertTemp')
                        if not update_product_df.empty:
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, update_product_df, f'{self.Req_id}_SupplierProductDetailsUpdateTemp')

                        '''Inserting and Updating the Chemical Property'''
                        temp_exists_non_smile_spd_df = self.cat_match_in_spd[
                            ['SupplierProductId', 'SupplierCatalogId']].merge(exists_not_smile_df, on='SupplierCatalogId', how='inner')
                        new_chemical_property_df, update_property_df, product_properties_audits_df, disable_df = self.process_chemical_properties(temp_exists_non_smile_spd_df)
                        if not new_chemical_property_df.empty:
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, new_chemical_property_df, f'{self.Req_id}_SupplierChemicalPropertiesInsertTemp')
                        if not update_property_df.empty:
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, update_property_df, f'{self.Req_id}_SupplierChemicalPropertiesUpdateTemp')
                        if not product_properties_audits_df.empty:
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, product_properties_audits_df, f'{self.Req_id}_PropertyAuditInsertTemp')
                        if not disable_df.empty:
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, disable_df, f'{self.Req_id}_SupplierChemicalPropertiesUpdateTemp')

                    logger.info("Finding difference of smile in df")
                    if not exists_have_smile_df.empty:
                        exists_have_smile_df['InChiKey'] = exists_have_smile_df['Smile'].apply(self.smiles_to_inchikey)

                        invalid_mask = exists_have_smile_df['InChiKey'].isna() | \
                                       (exists_have_smile_df['InChiKey'] == '') | \
                                       (exists_have_smile_df['InChiKey'].astype(str).str.lower() == 'nan')
                        invalid_smiles_df = exists_have_smile_df[invalid_mask].copy()
                        if not invalid_smiles_df.empty:
                            invalid_smiles_df['Reason'] = 'Smile are Invalid'
                            invalid_smiles_df['MainProductId'] = None
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, invalid_smiles_df, f'{self.Req_id}_InvalidSmileProduct')

                        exists_have_smile_df = exists_have_smile_df[~invalid_mask].copy()
                        if not exists_have_smile_df.empty:
                            self.map_inchi_with_chemindex_table(exists_have_smile_df)

                            if not self.not_exists_inchi_in_chem.empty:
                                temp_df = self.not_exists_inchi_in_chem[['Smile']]
                                column_name_mapping = {
                                    'Smile': 'smile'
                                }
                                temp_df = temp_df.rename(columns=column_name_mapping)
                                is_chem_proceed, message = chemindex.chemimdex_process(temp_df, self.Req_id)

                                if is_chem_proceed:
                                    self.map_inchi_with_chemindex_table(exists_have_smile_df)
                                else:
                                    raise Exception(message)

                            if not self.exists_inchi_in_chem.empty:
                                self.map_chem_id_with_mp_table(self.exists_inchi_in_chem)

                                # inserting non exists chemid in main productid
                                if not self.non_exists_chem_product_in_mp.empty:
                                    new_mp_product_df = self.non_exists_chem_product_in_mp.copy()
                                    new_mp_product_df.drop_duplicates(subset='CI_ChemProductId', keep='first', inplace=True)
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, new_mp_product_df, f'{self.Req_id}_MainProductsInsertTemp')
                                    insert_data_into_mongodb(self.temp_data_mongo_dbname, self.non_exists_chem_product_in_mp, f'{self.Req_id}_SupplierProductDetailsMappingUpdateTemp')

                                # Updating Existing ChemProductId in MainProduct Update MainProductId reference in SPD
                                if not self.exists_chem_product_in_mp.empty:

                                    exists_spd_df = self.cat_match_in_spd[self.cat_match_in_spd['SupplierCatalogId'].isin(self.exists_chem_product_in_mp['SupplierCatalogId'])]
                                    update_product_df, product_audit_df = self.find_differences(exists_spd_df, self.exists_chem_product_in_mp)

                                    if not product_audit_df.empty:
                                        insert_data_into_mongodb(self.temp_data_mongo_dbname, product_audit_df, f'{self.Req_id}_ProductAuditInsertTemp')
                                    if not update_product_df.empty:
                                        insert_data_into_mongodb(self.temp_data_mongo_dbname, update_product_df, f'{self.Req_id}_SupplierProductDetailsUpdateTemp')

                                    '''Inserting and Updating the Chemical Property'''
                                    temp_exists_spd_df = self.cat_match_in_spd[['SupplierProductId', 'SupplierCatalogId']].merge(self.exists_chem_product_in_mp, on='SupplierCatalogId', how='inner')
                                    new_chemical_property_df, update_property_df, product_properties_audits_df, disable_df = self.process_chemical_properties(temp_exists_spd_df)
                                    if not new_chemical_property_df.empty:
                                        insert_data_into_mongodb(self.temp_data_mongo_dbname, new_chemical_property_df, f'{self.Req_id}_SupplierChemicalPropertiesInsertTemp')
                                    if not update_property_df.empty:
                                        insert_data_into_mongodb(self.temp_data_mongo_dbname, update_property_df, f'{self.Req_id}_SupplierChemicalPropertiesUpdateTemp')
                                    if not product_properties_audits_df.empty:
                                        insert_data_into_mongodb(self.temp_data_mongo_dbname, product_properties_audits_df, f'{self.Req_id}_PropertyAuditInsertTemp')
                                    if not disable_df.empty:
                                        insert_data_into_mongodb(self.temp_data_mongo_dbname, disable_df, f'{self.Req_id}_SupplierChemicalPropertiesUpdateTemp')

                if not only_audit_df.empty:

                    # exists_have_smile_df, exists_not_smile_df = self.check_smile(only_audit_df)

                    logger.info("Finding difference of not smile in df")
                    if not only_audit_df.empty:
                        exists_non_smile_spd_df = self.cat_match_in_spd[self.cat_match_in_spd['SupplierCatalogId'].isin(only_audit_df['SupplierCatalogId'])]
                        if 'MainProductId' not in only_audit_df.columns:
                            only_audit_df['MainProductId'] = None
                        update_product_df, product_audit_df = self.find_differences(exists_non_smile_spd_df, only_audit_df, audit_only=True)

                        if not product_audit_df.empty:
                            insert_data_into_mongodb(self.temp_data_mongo_dbname, product_audit_df, f'{self.Req_id}_ProductAuditInsertTemp')

                    # logger.info("Finding difference of smile in df")
                    # if not only_audit_df.empty:
                    #     only_audit_df['InChiKey'] = only_audit_df['Smile'].apply(self.smiles_to_inchikey)
                    #
                    #     invalid_mask = only_audit_df['InChiKey'].isna() | \
                    #                    (only_audit_df['InChiKey'] == '') | \
                    #                    (only_audit_df['InChiKey'].astype(str).str.lower() == 'nan')
                    #     invalid_smiles_df = only_audit_df[invalid_mask].copy()
                    #     if not invalid_smiles_df.empty:
                    #         invalid_smiles_df['Reason'] = 'Smile are Invalid'
                    #         invalid_smiles_df['MainProductId'] = None
                    #         insert_data_into_mongodb(self.temp_data_mongo_dbname, invalid_smiles_df, f'{self.Req_id}_InvalidSmileProduct')
                    #
                    #     exists_have_smile_df = only_audit_df[~invalid_mask].copy()
                    #     if not exists_have_smile_df.empty:
                    #         self.map_inchi_with_chemindex_table(exists_have_smile_df)
                    #
                    #         if not self.not_exists_inchi_in_chem.empty:
                    #             temp_df = self.not_exists_inchi_in_chem[['Smile']]
                    #             column_name_mapping = {
                    #                 'Smile': 'smile'
                    #             }
                    #             temp_df = temp_df.rename(columns=column_name_mapping)
                    #             is_chem_proceed, message = chemindex.chemimdex_process(temp_df, self.Req_id)
                    #
                    #             if is_chem_proceed:
                    #                 self.map_inchi_with_chemindex_table(exists_have_smile_df)
                    #             else:
                    #                 raise Exception(message)
                    #
                    #         if not self.exists_inchi_in_chem.empty:
                    #             self.map_chem_id_with_mp_table(self.exists_inchi_in_chem)
                    #
                    #             # Updating Existing ChemProductId in MainProduct Update MainProductId reference in SPD
                    #             if not self.exists_chem_product_in_mp.empty:
                    #
                    #                 exists_spd_df = self.cat_match_in_spd[
                    #                     self.cat_match_in_spd['SupplierCatalogId'].isin(self.exists_chem_product_in_mp['SupplierCatalogId'])]
                    #                 update_product_df, product_audit_df = self.find_differences(exists_spd_df, self.exists_chem_product_in_mp, audit_only=True)
                    #
                    #                 if not product_audit_df.empty:
                    #                     insert_data_into_mongodb(self.temp_data_mongo_dbname, product_audit_df, f'{self.Req_id}_ProductAuditInsertTemp')
                    #
                    #             if not self.non_exists_chem_product_in_mp.empty:
                    #
                    #                 exists_spd_df = self.cat_match_in_spd[
                    #                     self.cat_match_in_spd['SupplierCatalogId'].isin(self.non_exists_chem_product_in_mp['SupplierCatalogId'])]
                    #                 if 'MainProductId' not in self.non_exists_chem_product_in_mp.columns:
                    #                     self.non_exists_chem_product_in_mp['MainProductId'] = None
                    #                 update_product_df, product_audit_df = self.find_differences(exists_spd_df, self.non_exists_chem_product_in_mp, audit_only=True)
                    #
                    #                 if not product_audit_df.empty:
                    #                     insert_data_into_mongodb(self.temp_data_mongo_dbname, product_audit_df, f'{self.Req_id}_ProductAuditInsertTemp')

            '''New Catalog Product'''
            logger.info("New Catalog Product")
            # Check Smile is available in sheet or not
            if not self.non_match_cat_in_df.empty:

                '''Converting into inserting format of SupplierProductProperties'''
                new_chemical_property_df = self.insert_chemical_properties(self.non_match_cat_in_df)
                if not new_chemical_property_df.empty:
                    insert_data_into_mongodb(self.temp_data_mongo_dbname, new_chemical_property_df, f'{self.Req_id}_SupplierChemicalPropertiesInsertTemp')

                have_smile_df, not_smile_df = self.check_smile(self.non_match_cat_in_df)
                if not not_smile_df.empty:
                    if 'MainProductId' not in not_smile_df.columns:
                        not_smile_df['MainProductId'] = None
                    insert_data_into_mongodb(self.temp_data_mongo_dbname, not_smile_df, f'{self.Req_id}_SupplierProductDetailsInsertTemp')

                    '''Converting into inserting format of supplierchemicalproperties'''
                    new_chemical_property_df = self.insert_chemical_properties(not_smile_df)
                    if not new_chemical_property_df.empty:
                        insert_data_into_mongodb(self.temp_data_mongo_dbname, new_chemical_property_df, f'{self.Req_id}_SupplierChemicalPropertiesInsertTemp')

                # Checking Smile is exists or not in MainProduct table
                if not have_smile_df.empty:
                    have_smile_df['InChiKey'] = have_smile_df['Smile'].apply(self.smiles_to_inchikey)
                    invalid_mask = have_smile_df['InChiKey'].isna() | \
                                   (have_smile_df['InChiKey'] == '') | \
                                   (have_smile_df['InChiKey'].astype(str).str.lower() == 'nan')
                    invalid_smiles_df = have_smile_df[invalid_mask].copy()
                    if not invalid_smiles_df.empty:
                        invalid_smiles_df['Reason'] = 'Smile are Invalid'
                        invalid_smiles_df['MainProductId'] = None
                        insert_data_into_mongodb(self.temp_data_mongo_dbname, invalid_smiles_df, f'{self.Req_id}_InvalidSmileProduct')
                        insert_data_into_mongodb(self.temp_data_mongo_dbname, invalid_smiles_df, f'{self.Req_id}_SupplierProductDetailsInsertTemp')

                    have_smile_df = have_smile_df[~invalid_mask].copy()
                    self.map_inchi_with_chemindex_table(have_smile_df)

                if not self.not_exists_inchi_in_chem.empty:
                    temp_df = self.not_exists_inchi_in_chem[['Smile']]
                    column_name_mapping = {
                        'Smile': 'smile'
                    }
                    temp_df = temp_df.rename(columns=column_name_mapping)
                    is_chem_proceed, message = chemindex.chemimdex_process(temp_df, self.Req_id)

                    if is_chem_proceed:
                        self.map_inchi_with_chemindex_table(have_smile_df)
                    else:
                        raise Exception(message)

                if not self.not_exists_inchi_in_chem.empty:
                    self.not_exists_inchi_in_chem['MainProductId'] = None
                    insert_data_into_mongodb(self.temp_data_mongo_dbname, self.not_exists_inchi_in_chem, f'{self.Req_id}_SupplierProductDetailsInsertTemp')

                if not self.exists_inchi_in_chem.empty:
                    self.map_chem_id_with_mp_table(self.exists_inchi_in_chem)

                    if not self.non_exists_chem_product_in_mp.empty:
                        new_mp_product_df = self.non_exists_chem_product_in_mp.copy()
                        new_mp_product_df.drop_duplicates(subset='CI_ChemProductId', keep='first', inplace=True)
                        insert_data_into_mongodb(self.temp_data_mongo_dbname, new_mp_product_df, f'{self.Req_id}_MainProductsInsertTemp')
                        insert_data_into_mongodb(self.temp_data_mongo_dbname, self.non_exists_chem_product_in_mp, f'{self.Req_id}_SupplierProductDetailsMappingInsertTemp')

                    # inserting exists MainProducts Data in SPD
                    if not self.exists_chem_product_in_mp.empty:
                        insert_data_into_mongodb(self.temp_data_mongo_dbname, self.exists_chem_product_in_mp, f'{self.Req_id}_SupplierProductDetailsInsertTemp')

        '''Read Data From SupplierProductDetailsInsertTemp or SupplierProductDetailsUpdateTemp or InvalidSmileProduct'''
        update_product_df = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_SupplierProductDetailsUpdateTemp')
        map_new_product_df = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_SupplierProductDetailsMappingInsertTemp')
        direct_new_product = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_SupplierProductDetailsInsertTemp')
        error_product_count = read_data_from_mongo(self.temp_data_mongo_dbname, f'{self.Req_id}_InvalidSmileProduct')

        new_product = pd.concat([map_new_product_df, direct_new_product])

        if not update_product_df.empty:
            update_product_df.to_csv(f'{sys.argv[3]}/{self.Req_id}_Update_Data_Count.csv', index=False)
        if not new_product.empty:
            new_product.to_csv(f'{sys.argv[3]}/{self.Req_id}_New_Data_Count.csv', index=False)
        if not error_product_count.empty:
            error_product_count.to_csv(f'{sys.argv[3]}/{self.Req_id}_Error_Data_Count.csv', index=False)
        
        # Generate report JSON
        report_json = self.generate_report_json(update_product_df, new_product, error_product_count)
        
        print("Generated Report JSON:")
        print("\n\n")
        print("=========================================")
        print(report_json)
        print("=========================================")
        print("\n\n")

        # Check for existing report
        existing_report = self.check_existing_report(
            self.Req_id,
            supplier_id := report_json['supplier_id']
        )

        if existing_report:
            logger.info(f"Existing report found for request_id: {self.Req_id}, supplier_id: {supplier_id}. Updating report.")
            report_json['_id'] = existing_report['_id']
            insert_data_into_mongodb(self.request_mongo_dbname, pd.DataFrame([report_json]), 'request_report', update=True, update_field='product')
        else:
            logger.info(f"No existing report found for request_id: {self.Req_id}, supplier_id: {supplier_id}. Inserting new report.")
            insert_data_into_mongodb(self.request_mongo_dbname, pd.DataFrame([report_json]), 'request_report')


if __name__ == "__main__":
    config = ConfigManager()
    data = {
        'Server': config.get_value("CREDENTIAL", "SERVER"),
        'Database': config.get_value("CREDENTIAL", "DATABASE"),
        'Username': config.get_value("CREDENTIAL", "USERNAME"),
        'Password': config.get_value("CREDENTIAL", "PASSWORD"),
        'ChemServer': config.get_value("ChemIndex_CREDENTIAL", "SERVER"),
        'ChemDb': config.get_value("ChemIndex_CREDENTIAL", "DATABASE"),
        'REQUEST_MONGO_DBNAME': config.get_value("MongoDB", "REQUEST_MONGO_DBNAME")
    }

    print(data)
    product_import = DataUpload(
        server=data['Server'],
        database=data['Database'],
        username=data['Username'],
        password=data['Password'],
        chem_server=data['ChemServer'],
        chem_db=data['ChemDb'],
        request_mongo_dbname=data['REQUEST_MONGO_DBNAME']
    )
    product_import.process_data()


"""
python client/Scripts/ProductImport/main.py EchemNewTest 791_ProductExcel /home/<USER>/pythonProjects/data_upload/test/new-echem-data-upload/data-upload-worker/ProductProcessQueue/client/Temp/ProductImport 791 "2025-06-13 08:54:26.639000"
"""
