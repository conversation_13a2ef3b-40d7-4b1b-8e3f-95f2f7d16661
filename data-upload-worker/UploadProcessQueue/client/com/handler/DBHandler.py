from client import config, pd
from pymongo import MongoClient
from client.com.handler.LoggerHandler import LoggerHandler

log = LoggerHandler()
URL = config.get('MongoDB', 'URL')
TEMP_DATA_MONGO_DBNAME = config.get('MongoDB', 'TEMP_DATA_MONGO_DBNAME')


def get_collection_names():
    log.request_logger.info("Getting Collection")
    client = MongoClient(URL)
    db = client[TEMP_DATA_MONGO_DBNAME]
    collection_names = db.list_collection_names()

    client.close()
    return collection_names


def read_data_from_mongo(coll_name):
    log.request_logger.info(f"Read Data from Mongodb {coll_name}")
    client = MongoClient(URL)
    db = client[TEMP_DATA_MONGO_DBNAME]
    collection = db[coll_name]  # Access the specified collection
    data = list(collection.find())
    client.close()
    product_df = pd.DataFrame(data)
    if "_id" in product_df.columns:
        product_df["_id"] = product_df["_id"].astype(str)  # Convert "_id" to string if needed
    return product_df


def delete_collection(collection_name):
    client = MongoClient(URL)
    db = client[TEMP_DATA_MONGO_DBNAME]
    db.drop_collection(collection_name)  # Deletes the collection from MongoDB
    log.request_logger.info(f"Collection {collection_name} deleted successfully.")
