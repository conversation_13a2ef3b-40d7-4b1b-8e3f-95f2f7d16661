from client.com.handler.DBHandler import get_collection_names, read_data_from_mongo, delete_collection
from client import pyodbc, config, pd, math, time
from client.com.handler.MainProductImportHandler import MainProductImportHandler
from client.com.handler.ProductImportHandler import ProductImportHandler
from client.com.handler.PriceImportHandler import PriceImportHandler
from client.com.handler.StockImportHandler import StockImportHandler

server = config.get("CREDENTIAL", "SERVER")
database = config.get("CREDENTIAL", "DATABASE")
username = config.get("CREDENTIAL", "USERNAME")
password = config.get("CREDENTIAL", "PASSWORD")


class UploadHandler(MainProductImportHandler, ProductImportHandler, PriceImportHandler, StockImportHandler):

    def __init__(self):
        super().__init__()
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.spd_df = pd.DataFrame()
        self.price_df = pd.DataFrame()
        self.conn = None

    def response(self):
        try:
            return {
                "ImportRequestId": self.ImportRequestId,
                "RequestTypeId": self.RequestTypeId,
                "Status": self.Status,
                "Error": self.Error,
                "UploadTimeTaken": self.TimeTaken,
                "QueueName": "upload_queue",
                "OutputFiles": [],
            }
        except Exception as e:
            self.request_logger.error(e, exc_info=True)

    def connect_to_database(self):
        try:
            conn_string = f"Driver={{SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            print(conn_string)
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)

    def map_product_in_mp(self, df):
        self.connect_to_database()
        query = """select * from MainProducts"""
        mp_df = pd.read_sql(query, self.conn)
        new_df = df.merge(mp_df[['CI_ChemProductId', 'MainProductId']], on='CI_ChemProductId', how='inner')
        self.conn.close()
        return new_df

    def map_temp_id_with_spd(self, df):
        self.connect_to_database()

        catalog_ids = df['SupplierCatalogId'].unique().tolist()
        batch_size = 1000
        batches = math.ceil(len(catalog_ids) / batch_size)
        supplier_index = df['SupplierId'].first_valid_index()
        supplier_id = df.at[supplier_index, 'SupplierId'] if supplier_index is not None else None

        self.spd_df = pd.DataFrame()  # Store all matching records

        # Step 1: Fetch all matching records in batches
        for i in range(batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(catalog_ids))
            catalog_ids_batch = catalog_ids[start_idx:end_idx]

            catalog_ids_str = ", ".join(f"'{str(id)}'" for id in catalog_ids_batch)
            query = f"""SELECT * FROM [dbo].[SupplierProducts]
                        WHERE SupplierCatalogId IN ({catalog_ids_str})
                        and OmsEchemPortalSupplierId = {supplier_id}"""

            batch_result = pd.read_sql(query, self.conn)
            self.spd_df = pd.concat([self.spd_df, batch_result], ignore_index=True)

        # Step 2: Merge once all data is fetched
        df = df.merge(self.spd_df[['SupplierCatalogId', 'SupplierProductId']], on='SupplierCatalogId', how='inner')
        # self.spd_df = self.spd_df.merge(price_df[['SupplierCatalogId', '_id']], on='SupplierCatalogId', how='inner')
        # self.spd_df.rename(columns={'_id': 'TempID'}, inplace=True)

        self.conn.close()
        return df

    def map_chemical_property_cat_with_spd_cat(self, df):
        self.connect_to_database()

        catalog_ids = df['SupplierCatalogId'].unique().tolist()
        batch_size = 1000
        batches = math.ceil(len(catalog_ids) / batch_size)
        supplier_index = df['SupplierId'].first_valid_index()
        supplier_id = df.at[supplier_index, 'SupplierId'] if supplier_index is not None else None

        self.spd_df = pd.DataFrame()  # Store all matching records

        # Step 1: Fetch all matching records in batches
        for i in range(batches):
            start_idx = i * batch_size
            end_idx = min((i + 1) * batch_size, len(catalog_ids))
            catalog_ids_batch = catalog_ids[start_idx:end_idx]

            catalog_ids_str = ", ".join(f"'{str(id)}'" for id in catalog_ids_batch)
            query = f"""SELECT * FROM [dbo].[SupplierProducts]
                        WHERE SupplierCatalogId IN ({catalog_ids_str})
                        and OmsEchemPortalSupplierId = {supplier_id}"""

            batch_result = pd.read_sql(query, self.conn)
            self.spd_df = pd.concat([self.spd_df, batch_result], ignore_index=True)

        # Step 2: Merge once all data is fetched
        df = df.merge(self.spd_df[['SupplierCatalogId', 'SupplierProductId']], on='SupplierCatalogId', how='inner')

        self.conn.close()
        return df

    def handle_insertion(self, insert_temp_list):
        self.info(logs="Starting Inserting Process in Database")
        for coll_name in insert_temp_list:
            try:
                df = read_data_from_mongo(coll_name)
                if not df.empty:
                    # Process each collection based on the name and type
                    self.request_logger.info(f"Processing collection: {coll_name}")
                    if coll_name == f'{self.ImportRequestId}_MainProductsInsertTemp':
                        self.update_request_status(stage_id=6, sub_stage=1)
                        success, message = self.insert_into_mp(df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierProductDetailsMappingInsertTemp':
                        product_df = self.map_product_in_mp(df)
                        self.update_request_status(stage_id=6, sub_stage=2)
                        success, message = self.insert_into_spd(product_df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierProductDetailsInsertTemp':
                        self.update_request_status(stage_id=6, sub_stage=2)
                        success, message = self.insert_into_spd(df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_ProductAuditInsertTemp':
                        success, message = self.insert_into_product_audit(df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierChemicalPropertiesInsertTemp':
                        if 'SupplierProductId' in df.columns:
                            df = df.drop('SupplierProductId', axis=1)
                        chemical_df = self.map_chemical_property_cat_with_spd_cat(df)
                        success, message = self.insert_into_property(chemical_df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_PropertyAuditInsertTemp':
                        success, message = self.insert_into_property_audit(df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierProductPricesInsertTemp':
                        self.update_request_status(stage_id=6, sub_stage=3)
                        if self.RequestTypeId == 1:
                            if 'SupplierProductId' in df.columns:
                                df = df.drop('SupplierProductId', axis=1)
                            price_df = self.map_temp_id_with_spd(df)
                            success, message = self.insert_into_price(price_df)
                        else:
                            success, message = self.insert_into_price(df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_AuditPriceInsertTemp':
                        success, message = self.insert_into_price_audit(df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierProductStocksInsertTemp':
                        self.update_request_status(stage_id=6, sub_stage=4)
                        if self.RequestTypeId == 1:
                            if 'SupplierProductId' in df.columns:
                                df = df.drop('SupplierProductId', axis=1)
                            stock_df = self.map_temp_id_with_spd(df)
                            success, message = self.insert_into_stock(stock_df)
                            if success:
                                self.request_logger.info(message)
                                success, message = self.update_spd_availability(stock_df)
                        else:
                            success, message = self.insert_into_stock(df)
                            if success:
                                self.request_logger.info(message)
                                success, message = self.update_spd_availability(df)

                        if not success:
                            return False, message  # Stop if any step failed
                        self.result(logs=message)
                        self.request_logger.info(message)

                    elif coll_name == f'{self.ImportRequestId}_AuditStockInsertTemp':
                        success, message = self.insert_into_stock_audit(df)
                        if not success:
                            return False, message  # Return error message if insertion fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message
                else:
                    self.result(logs=f"No data found in collection: {coll_name}")
                    self.request_logger.info(f"No data found in collection: {coll_name}")
            except Exception as e:
                self.request_logger.error(f"Error while processing collection {coll_name}: {e}")
                return False, f"Error during processing {coll_name}"

        return True, "All insert collections processed successfully."

    def handle_update(self, update_temp_list):
        """
        Handle the updating of all temp lists in sequence
        """
        for coll_name in update_temp_list:
            try:
                df = read_data_from_mongo(coll_name)
                if not df.empty:
                    # Process each collection based on the name and type
                    self.request_logger.info(f"Processing update collection: {coll_name}")

                    if coll_name == f'{self.ImportRequestId}_SupplierProductDetailsMappingUpdateTemp':
                        product_df = self.map_product_in_mp(df)
                        success, message = self.update_product(product_df)
                        if not success:
                            return False, message  # Return error message if update fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierProductDetailsUpdateTemp':
                        self.update_request_status(stage_id=6, sub_stage=6)
                        success, message = self.update_product(df)
                        if not success:
                            return False, message  # Return error message if update fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierChemicalPropertiesUpdateTemp':
                        success, message = self.update_chemical_property(df)
                        if not success:
                            return False, message  # Return error message if update fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierProductPricesUpdateTemp':
                        self.update_request_status(stage_id=6, sub_stage=7)
                        success, message = self.update_price(df)
                        if not success:
                            return False, message  # Return error message if update fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message

                    elif coll_name == f'{self.ImportRequestId}_SupplierProductStocksUpdateTemp':
                        self.update_request_status(stage_id=6, sub_stage=8)
                        success, message = self.update_stock(df)
                        if success:
                            self.request_logger.info(message)
                            success, message = self.update_spd_availability(df)
                        if not success:
                            return False, message  # Return error message if update fails
                        self.result(logs=message)
                        self.request_logger.info(message)  # Log the success message
                else:
                    self.result(logs=f"No data found in collection: {coll_name}")
                    self.request_logger.info(f"No data found in collection: {coll_name}")
            except Exception as e:
                self.request_logger.error(f"Error while processing update collection {coll_name}: {e}")
                return False, f"Error during processing {coll_name}"

        return True, "All update collections processed successfully."

    def delete_collection(self, coll_list):
        for coll_name in coll_list:
            try:
                delete_collection(coll_name)  # Function to delete the collection
                self.info(logs=f"Deleted collection: {coll_name}")
                self.request_logger.info(f"Deleted collection: {coll_name}")
            except Exception as e:
                self.request_logger.error(f"Error deleting collection {coll_name}: {str(e)}")

    def start_importing_process(self):
        start_time = time.time()
        collection_list = get_collection_names()
        current_collection_list = [name for name in collection_list if str(self.ImportRequestId) in name]
        try:
            self.Status = "Inprogress"
            self.update_request_status(stage_id=6)

            self.result(logs=f"Getting collection of Temp Collection base on RequestID: {current_collection_list}")

            temp_list = [name for name in current_collection_list if 'Temp' in name]
            insert_temp_list = [name for name in temp_list if 'Insert' in name]
            update_temp_list = [name for name in temp_list if 'Update' in name]

            main_product_insert_list = [name for name in insert_temp_list if 'MainProducts' in name]
            product_temp_list = [name for name in insert_temp_list if 'ProductDetails' in name]
            properties_temp_list = [name for name in insert_temp_list if 'ChemicalProperties' in name]
            audit_list = [name for name in insert_temp_list if 'Audit' in name]
            price_temp_list = [name for name in insert_temp_list if 'ProductPrices' in name]
            stock_temp_list = [name for name in insert_temp_list if 'ProductStocks' in name]

            product_update_list = [name for name in update_temp_list if 'ProductDetails' in name]
            properties_update_list = [name for name in update_temp_list if 'ChemicalProperties' in name]
            price_update_list = [name for name in update_temp_list if 'ProductPrices' in name]
            stock_update_list = [name for name in update_temp_list if 'ProductStocks' in name]

            # Combine them in the desired order: Product -> Price -> Stock
            insert_temp_list = main_product_insert_list + product_temp_list + properties_temp_list + price_temp_list + stock_temp_list + audit_list
            update_temp_list = product_update_list + properties_update_list + price_update_list + stock_update_list

            self.request_logger.info(f"All Temp Names: {temp_list}")
            self.request_logger.info(f"Insert Temp List: {insert_temp_list}")
            self.request_logger.info(f"Update Temp List: {update_temp_list}")
            self.result(logs=f"All Temp Names: {temp_list}")
            self.result(logs=f"Insert Temp List: {insert_temp_list}")
            self.result(logs=f"Update Temp List: {update_temp_list}")

            # Handle Insert operations first
            insert_success, insert_message = self.handle_insertion(insert_temp_list)
            if not insert_success:
                self.Status = "Canceled"
                self.Error = insert_message
                self.update_request_status(stage_id=6)
                self.error_log(logs=self.Error)
                self.delete_collection(current_collection_list)
                return self.response()  # Return the response with error status

            # Handle Update operations next (if applicable)
            update_success, update_message = self.handle_update(update_temp_list)
            if not update_success:
                self.Status = "Canceled"
                self.Error = update_message
                self.update_request_status(stage_id=6)
                self.error_log(logs=self.Error)
                self.delete_collection(current_collection_list)
                return self.response()  # Return the response with error status

            # Import process was successful
            end_time = time.time()
            elapsed_time = end_time - start_time
            hours = int(elapsed_time // 3600)
            elapsed_time %= 3600
            minutes = int(elapsed_time // 60)
            seconds = elapsed_time % 60
            self.TimeTaken = f"{hours} hours, {minutes} minutes, {seconds:.2f} seconds"
            self.Status = "Finished"
            self.Error = None
            self.update_request_status(stage_id=6, sub_stage=5)
            self.result(logs="Import process completed successfully.")
            self.request_logger.info("Import process completed successfully.")
            self.delete_collection(current_collection_list)
            return self.response()  # Return the response with success status
        except Exception as e:
            end_time = time.time()
            elapsed_time = end_time - start_time
            hours = int(elapsed_time // 3600)
            elapsed_time %= 3600
            minutes = int(elapsed_time // 60)
            seconds = elapsed_time % 60
            self.TimeTaken = f"{hours} hours, {minutes} minutes, {seconds:.2f} seconds"
            # Handle any exceptions that occur during the importing process
            self.request_logger.error(f"Error during importing process: {str(e)}")
            self.Status = "Canceled"
            self.Error = f"Error during importing process: {str(e)}"
            self.error_log(logs=self.Error)
            self.update_request_status(stage_id=6)
            self.delete_collection(current_collection_list)

            return self.response()  # Return the response with error status
