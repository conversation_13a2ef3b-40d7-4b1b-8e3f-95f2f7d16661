from client import pyodbc, config, platform, pd
import numpy as np
from sqlalchemy.types import Integer, NVARCHAR
from sqlalchemy import create_engine
from client.com.handler.LoggerHandler import LoggerHandler
from client.com.handler.RequestHandler import RequestHandler

log = LoggerHandler()
server = config.get("CREDENTIAL", "SERVER")
database = config.get("CREDENTIAL", "DATABASE")
username = config.get("CREDENTIAL", "USERNAME")
password = config.get("CREDENTIAL", "PASSWORD")


class ProductImportHandler(RequestHandler):

    def __init__(self):
        super().__init__()
        self.server = server
        self.database = database
        self.username = username
        self.password = password
        self.conn = None

    def connect_to_database(self):
        try:
            conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
            print(conn_string)
            self.conn = pyodbc.connect(conn_string)
        except (Exception,):
            conn_string = (
                f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                f"SERVER={self.server};"
                f"DATABASE={self.database};"
                f"UID={self.username};"
                f"PWD={self.password};"
                "Encrypt=yes;TrustServerCertificate=yes;"
            )
            self.conn = pyodbc.connect(conn_string)

    def update_chemical_property(self, df):
        try:
            created_date_str = self.CreatedDate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            log.request_logger.info(f"Updating data into the SupplierProductProperties table.")
            self.connect_to_database()

            # Replace NaN with 'Null' for SQL-friendly values
            new_df = df.where(pd.notna(df), 'Null')
            if 'PropertyValue' in new_df.columns:
                new_df['PropertyValue'] = new_df['PropertyValue'].astype(str).replace("'", "''", regex=True)

            batch_size = 1000
            total_rows = len(new_df)

            cursor = self.conn.cursor()

            for start in range(0, total_rows, batch_size):
                end = min(start + batch_size, total_rows)
                batch_df = new_df.iloc[start:end]

                # Generate CASE statements for PropertyValue updates
                case_statements = [
                    f"""WHEN SupplierProductId = '{row['SupplierProductId']}' AND PropertyNameId = {row['PropertyNameId']}
                        THEN {'NULL' if row['PropertyValue'] == 'Null' else f"N'{row['PropertyValue']}'"}"""
                    for _, row in batch_df.iterrows()
                ]

                # Generate CASE statements for IsDisable updates (only if column is present)
                disable_case_statements = []
                if 'IsDisable' in batch_df.columns:
                    disable_case_statements = [
                        f"""WHEN SupplierProductId = '{row['SupplierProductId']}' AND PropertyNameId = {row['PropertyNameId']}
                                        THEN {'NULL' if row['IsDisable'] == 'Null' else row['IsDisable']}"""
                        for _, row in batch_df.iterrows()
                    ]

                # Create the bulk update query
                bulk_update_query = f"""
                            UPDATE SupplierProductProperties
                            SET PropertyValue = CASE 
                                {' '.join(case_statements)}
                                ELSE PropertyValue
                            END
                            {',' if disable_case_statements else ''}
                            {"IsDisable = CASE " + ' '.join(disable_case_statements) + " ELSE IsDisable END" if disable_case_statements else ''}
                            {',' if disable_case_statements or case_statements else ''}
                            UpdatedAt = '{created_date_str}'
                            WHERE {" OR ".join(f"(SupplierProductId = '{row['SupplierProductId']}' AND PropertyNameId = {row['PropertyNameId']})" for _, row in batch_df.iterrows())};
                        """

                print(bulk_update_query)
                cursor.execute(bulk_update_query)

            self.conn.commit()
            cursor.close()
            self.conn.close()

            return True, f"Successfully updated data in SupplierProductProperties."
        except Exception as e:
            log.request_logger.error(f"Error updating SupplierProductProperties: {e}")
            return False, f"Error updating SupplierProductProperties: {e}"

    def update_product(self, df):
        try:
            created_date_str = self.CreatedDate.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            log.request_logger.info(f"Updating data into the product table.")
            self.connect_to_database()

            # Replace NaN with 'Null'
            new_df = df.where(pd.notna(df), 'Null')
            if 'ProductName' in new_df.columns:
                new_df['ProductName'] = new_df['ProductName'].astype(str).replace("'", "''", regex=True)

            batch_size = 1000
            total_rows = len(new_df)

            cursor = self.conn.cursor()

            for start in range(0, total_rows, batch_size):
                end = min(start + batch_size, total_rows)
                batch_df = new_df.iloc[start:end]

                def generate_case_statements(column_name, is_int=False):
                    case_list = [
                        f"WHEN SupplierCatalogId = '{row['SupplierCatalogId']}' THEN {row[column_name]}"
                        if is_int else
                        f"WHEN SupplierCatalogId = '{row['SupplierCatalogId']}' THEN N'{row[column_name]}'"
                        if row[column_name] != 'Null' and pd.notna(row[column_name])
                        else f"WHEN SupplierCatalogId = '{row['SupplierCatalogId']}' THEN NULL"
                        for _, row in batch_df.iterrows()
                    ]
                    
                    return case_list if any(row[column_name] != 'Null' for _, row in batch_df.iterrows()) else None

                # Generate CASE statements dynamically
                case_statements = {
                    col: generate_case_statements(col, is_int=(col in ['MainProductId']))  # Add all BIGINT columns
                    for col in [
                        'MainProductId', 'ProductName', 'CASNumber', 'Purity', 'MDLNumber',
                        'SupplierId', 'Url', 'SupplierName', 'Smile'
                    ]
                }

                # Join CASE statements into query strings or use NULL if all values are NULL
                case_queries = {
                    key: f"CASE {' '.join(value)} END" if value else "NULL"
                    for key, value in case_statements.items()
                }

                # Construct bulk update query
                bulk_update_query = f'''
                        UPDATE SupplierProducts
                        SET
                            MainProductId = {case_queries['MainProductId']},
                            ProductName = {case_queries['ProductName']},
                            CASNumber = {case_queries['CASNumber']},
                            Purity = {case_queries['Purity']},
                            MDLNumber = {case_queries['MDLNumber']},
                            Smile = {case_queries['Smile']},
                            Url = {case_queries['Url']},
                            UpdatedAt = '{created_date_str}',
                            LastUpdatedAt = '{created_date_str}'
                        WHERE SupplierCatalogId IN ({', '.join(f"'{id}'" for id in batch_df['SupplierCatalogId'])})
                        AND OmsEchemPortalSupplierId = {new_df['SupplierId'].iloc[0]};
                    '''

                print(bulk_update_query)
                cursor.execute(bulk_update_query)
            self.conn.commit()
            cursor.close()
            self.conn.close()
            return True, f"Successfully updated data into the product table."
        except Exception as e:
            log.request_logger.error(f"Error updating data into product table: {e}")
            return False, f"Error updating data into product table: {e}"

    def insert_into_product_audit(self, df):
        try:
            log.request_logger.info(f"Inserting data into product audit table.")
            new_df = df[['SupplierProductId', 'ColumnName', 'Value', 'CreatedAt']]
            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                sql_types = {
                    'SupplierProductId': Integer(),
                    'Value': NVARCHAR(),
                }
                new_df.to_sql('SupplierProductAudits', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size, dtype=sql_types)
            return True, f"Successfully inserted data into product audit table."
        except Exception as e:
            log.request_logger.error(f"Error inserting product audit data: {e}")
            return False, f"Error inserting product audit data: {e}"

    def insert_into_spd(self, df):
        try:
            df['CreatedAt'] = self.CreatedDate
            df['LastUpdatedAt'] = self.CreatedDate
            log.request_logger.info(f"Inserting data into the product table.")

            # Step 1: Remove duplicates based on SupplierId + SupplierCatalogId
            df = df.drop_duplicates(subset=['SupplierId', 'SupplierCatalogId'])

            new_df = df[['SupplierId', 'SupplierCatalogId', 'ProductName', 'CASNumber', 'MDLNumber', 'AvailabilityId',
                         'Url', 'MainProductId', 'Purity', 'SupplierName', 'Smile', 'CreatedAt', 'LastUpdatedAt']]
            new_df = new_df.rename(columns={
                "SupplierId": "OmsEchemPortalSupplierId",
                "SupplierName": "OmsEchemPortalSupplierName"
            })
            columns_to_check = ['CASNumber', 'MDLNumber', 'ProductName', 'Url', 'Purity', 'MainProductId', 'Smile']

            for col in columns_to_check:
                new_df[col] = new_df[col].where(new_df[col].notna(), None)

            system = platform.system()
            if not new_df.empty:
                log.request_logger.info(f"{system = }")
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                        )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                sql_types = {
                    'Url': NVARCHAR(length=500),
                    'Purity': NVARCHAR(length=50),
                    'ProductName': NVARCHAR(length=1000)
                }
                new_df.to_sql('SupplierProducts', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size, dtype=sql_types)
            return True, "Successfully inserted data into the product table."
        except Exception as e:
            log.request_logger.error(f"Error inserting data into product table: {e}")
            return False, f"Error inserting data into product table: {e}"

    def insert_into_property(self, df):
        try:
            df['CreatedAt'] = self.CreatedDate
            log.request_logger.info(f"Inserting data into chemical properties table.")

            # Step 1: Remove duplicates based on SupplierId + SupplierCatalogId
            df = df.drop_duplicates(subset=['SupplierProductId', 'PropertyNameId', 'PropertyValue'])

            new_df = df[['SupplierProductId', 'PropertyNameId', 'PropertyValue', 'CreatedAt']]
            new_df['PropertyValue'] = new_df['PropertyValue'].astype(str)

            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = (
                        f"Driver={{ODBC Driver 17 for SQL Server}};"
                        f"Server={self.server};"
                        f"Database={self.database};"
                        f"uid={self.username};pwd={self.password};"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")

                batch_size = 1000

                sql_types = {
                    'SupplierProductId': Integer(),
                    'PropertyNameId': Integer(),
                    'PropertyValue': NVARCHAR(length=3500)
                }
                new_df.to_sql('SupplierProductProperties', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size, dtype=sql_types)

            return True, f"Successfully inserted data into chemical properties table."
        except Exception as e:
            log.request_logger.error(f"Error inserting chemical properties data: {e}")
            return False, f"Error inserting chemical properties data: {e}"

    def insert_into_property_audit(self, df):
        try:
            df['CreatedAt'] = self.CreatedDate
            log.request_logger.info(f"Inserting data into property audit table.")

            # Step 1: Remove duplicates based on SupplierId + SupplierCatalogId
            df = df.drop_duplicates(subset=['SupplierProductId', 'PropertyNameId', 'PropertyValue'])

            new_df = df[['SupplierProductId', 'PropertyNameId', 'PropertyValue', 'CreatedAt']]
            system = platform.system()
            if not new_df.empty:
                if system == 'Linux':
                    conn_string = (
                        f"DRIVER={{ODBC Driver 18 for SQL Server}};"
                        f"SERVER={self.server};"
                        f"DATABASE={self.database};"
                        f"UID={self.username};"
                        f"PWD={self.password};"
                        "Encrypt=yes;TrustServerCertificate=yes;"
                    )
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                elif system == 'Windows':
                    conn_string = f"Driver={{ODBC Driver 17 for SQL Server}};Server={self.server};Database={self.database};uid={self.username};pwd={self.password};"
                    engine = create_engine(f"mssql+pyodbc:///?odbc_connect={conn_string}")
                batch_size = 1000
                sql_types = {
                    'SupplierProductId': Integer(),
                    'PropertyNameId': Integer(),
                    'PropertyValue': NVARCHAR(length=3500)
                }
                new_df.to_sql('SupplierProductPropertieAudits', con=engine, index=False, if_exists='append', schema='dbo', chunksize=batch_size, dtype=sql_types)
            return True, f"Successfully inserted data into property audit table."
        except Exception as e:
            log.request_logger.error(f"Error inserting property audit data: {e}")
            return False, f"Error inserting property audit data: {e}"

