from fastapi import APIRouter, Query, HTTPException, Depends

from app.schemas.request_report_schemas import ReportFilterSchema
from app.services.report_service import fetch_filtered_reports, generate_excel_dynamic
from app.services.db_service import get_db  # adjust if your db dependency import differs

router = APIRouter()

@router.get("/reports")
async def get_or_download_reports(
    filters: ReportFilterSchema = Depends(),
    db = Depends(get_db)
):
    data = await fetch_filtered_reports(
        db,
        filters.start_date,
        filters.end_date,
        filters.week,
        filters.supplier_id,
        filters.request_id
    )

    if filters.download:
        return await generate_excel_dynamic(data)
    
    return {"status": "success", "data": data}
