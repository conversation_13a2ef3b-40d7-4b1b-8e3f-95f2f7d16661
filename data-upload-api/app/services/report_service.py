from openpyxl import Workbook
from openpyxl.styles import Alignment, PatternFill, Border, Side
from fastapi.responses import StreamingResponse
from io import BytesIO
from typing import List
from datetime import datetime
from fastapi import HTTPException
from motor.motor_asyncio import AsyncIOMotorDatabase
from typing import Optional
from app.utils.settings import REQ_COLLECTION_NAME
from bson import ObjectId


async def fetch_filtered_reports(
    db: AsyncIOMotorDatabase,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    week: Optional[int] = None,
    supplier_id: Optional[int] = None,
    request_id: Optional[int] = None,
    request_group_id: Optional[str] = None
):
    query = {}

    # Only fetch requests with "Done" status (StatusId = 4) since only they have RequestReport data
    query["StatusObj.StatusId"] = 4

    # Only fetch requests that have RequestReport data
    query["RequestReport"] = {"$exists": True, "$ne": []}

    if start_date and end_date:
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            # Add one day to end_date to include the entire end date
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
            query["CreatedDate"] = {"$gte": start_dt, "$lte": end_dt}
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD.")

    if supplier_id:
        # Assuming supplier_id is stored in RequestTypeObj or similar field
        query["RequestTypeObj.RequestTypeID"] = supplier_id

    if request_id:
        # Convert to ObjectId if it's a valid ObjectId string, otherwise use as integer
        if isinstance(request_id, str) and ObjectId.is_valid(request_id):
            query["_id"] = ObjectId(request_id)
        else:
            query["_id"] = request_id

    if request_group_id:
        query["request_group_id"] = request_group_id

    # Remove status filter since we're only getting "Done" requests
    # if status:
    #     query["StatusObj.StatusName"] = status

    collection = db[REQ_COLLECTION_NAME]
    data = await collection.find(query).to_list(length=None)

    # Flatten the data to include RequestReport details
    flattened_data = []
    for item in data:
        item["_id"] = str(item["_id"])

        # If there are RequestReport entries, create a row for each
        if item.get("RequestReport"):
            for report in item["RequestReport"]:
                flattened_item = {
                    "_id": item["_id"],
                    "InputFileName": item.get("InputFileName"),
                    "CreatedDate": item.get("CreatedDate"),
                    "UpdatedDate": item.get("UpdatedDate"),
                    "StatusObj": item.get("StatusObj"),
                    "StageObj": item.get("StageObj"),
                    "RequestTypeObj": item.get("RequestTypeObj"),
                    "request_group_id": item.get("request_group_id"),
                    **report  # Include all RequestReport fields
                }
                flattened_data.append(flattened_item)
        else:
            # If no RequestReport, include the main item
            flattened_data.append(item)

    return flattened_data


def reorder_json(data: list):
    """
    Reorder the JSON fields into non-nested first, nested second.
    This works dynamically for any given structure.
    """
    reordered_data = []
    
    for item in data:
        non_nested = {}
        nested = {}

        # Separate non-nested and nested fields
        for key, value in item.items():
            if isinstance(value, dict):  # If value is a dictionary, it's nested
                nested[key] = value
            else:
                non_nested[key] = value

        # Append the non-nested first, then the nested fields
        reordered_item = {**non_nested, **nested}
        reordered_data.append(reordered_item)

    return reordered_data


def parse_json_headers(data: list):
    """
    Parses a list of dicts and returns:
    - header1: outer keys (flat keys first, then nested dict keys with blanks)
    - header2: sub keys for nested dicts, empty for flat keys
    - merge_ranges: dict of outer_key -> (start_col, end_col) for merging cells
    """
    if not data:
        return [], [], {}

    # Reorder data first (non-nested first, nested second)
    reordered_data = reorder_json(data)

    sample = reordered_data[0]

    header1 = []
    header2 = []
    merge_ranges = {}
    col_index = 1  # Excel column index starts at 1

    # First pass: collect flat keys and nested keys
    flat_keys = [k for k, v in sample.items() if not isinstance(v, dict)]
    nested_keys = [k for k, v in sample.items() if isinstance(v, dict)]

    # Process flat keys first
    for key in flat_keys:
        header1.append(key)
        header2.append("")
        col_index += 1

    # Process nested dict keys
    for key in nested_keys:
        value = sample[key]
        sub_keys = list(value.keys()) if value else []

        if len(sub_keys) > 0:
            # For each nested key, create the merged cells
            header1.append(key)  # Parent key
            header2.extend(sub_keys)  # Nested keys

            # Track merge range for parent key (will span multiple columns)
            merge_ranges[key] = (col_index, col_index + len(sub_keys) - 1)

            # Move column index for the next set of nested headers
            col_index += len(sub_keys)
        else:
            # Handle empty dictionaries as single columns
            header1.append(key)
            header2.append("")
            col_index += 1

    # Ensure header2 has enough columns
    while len(header2) < len(header1):
        header2.append("")

    return header1, header2, merge_ranges

async def generate_excel_dynamic(data: list, filename: str = "dynamic_report.xlsx"):
    wb = Workbook()
    ws = wb.active
    ws.title = "Dynamic Report"

    # -----------------
    # PARSE HEADERS
    # -----------------

    print(data)
    header1, header2, merge_ranges = parse_json_headers(data)
    print("\nParsed Headers:")
    print("Header1:", header1)
    print("Header2:", header2)      
    print("Merge Ranges:", merge_ranges)
    print("\n")

    ws.append(header1)
    ws.append(header2)

    # Define styles
    section_fills = {
        "product": PatternFill(start_color="D9EAD3", end_color="D9EAD3", fill_type="solid"),
        "price": PatternFill(start_color="FFD966", end_color="FFD966", fill_type="solid"),
        "stock": PatternFill(start_color="C9DAF8", end_color="C9DAF8", fill_type="solid"),
    }
    default_fill = PatternFill(start_color="B7C7D6", end_color="B7C7D6", fill_type="solid")
    dark_border = Border(
        left=Side(style="thin", color="000000"),
        right=Side(style="thin", color="000000"),
        top=Side(style="thin", color="000000"),
        bottom=Side(style="thin", color="000000")
    )

    # -----------------
    # MERGE CELLS & STYLE
    # -----------------
    for key, (start_col, end_col) in merge_ranges.items():
        # Only merge if end_col is greater than start_col (multiple columns)
        if end_col > start_col:
            cell = ws.cell(row=1, column=start_col)
            ws.merge_cells(start_row=1, start_column=start_col, end_row=1, end_column=end_col)
            cell.value = key
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.fill = section_fills.get(key, default_fill)

            # Apply border to merged cells
            for row in ws.iter_rows(min_row=1, max_row=1, min_col=start_col, max_col=end_col):
                for c in row:
                    c.border = dark_border
        else:
            # Single column, just style the cell
            cell = ws.cell(row=1, column=start_col)
            cell.value = key
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.fill = section_fills.get(key, default_fill)
            cell.border = dark_border

    # Style flat columns (non-nested keys) in first row
    for col in range(1, len(header1) + 1):
        if ws.cell(row=1, column=col).value not in merge_ranges.keys():
            cell = ws.cell(row=1, column=col)
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.fill = default_fill
            cell.border = dark_border

    # Style second row sub-headers
    for col in range(1, len(header2) + 1):
        cell = ws.cell(row=2, column=col)
        cell.alignment = Alignment(horizontal="center", vertical="center")
        outer = header1[col - 1] if col - 1 < len(header1) else ""
        cell.fill = section_fills.get(outer, default_fill)
        cell.border = dark_border

    # -----------------
    # WRITE DATA ROWS
    # -----------------
    for idx, item in enumerate(data):
        row = []
        for key, value in item.items():
            if isinstance(value, dict):
                if value:  # Non-empty dictionary
                    row.extend([value.get(sub_key) for sub_key in value.keys()])
                else:  # Empty dictionary
                    row.append("")  # Add empty cell for empty dict
            else:
                row.append(value)
        ws.append(row)

        # Apply alternating row colors & borders
        row_num = idx + 3  # data rows start from row 3
        fill = PatternFill(start_color="F9F9F9", end_color="F9F9F9", fill_type="solid") if row_num % 2 == 0 else PatternFill(start_color="FFFFFF", end_color="FFFFFF", fill_type="solid")
        for cell in ws[row_num]:
            cell.fill = fill
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = dark_border

    # -----------------
    # RETURN STREAMING RESPONSE
    # -----------------
    stream = BytesIO()
    wb.save(stream)
    stream.seek(0)

    return StreamingResponse(
        stream,
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )
