from typing import Optional
from pydantic import BaseModel, Field

class ChemIndexRequestReport(BaseModel):
    ChemIndexInputDataCount: Optional[int] = None
    ChemIndexNewDataCount: Optional[int] = None
    ChemIndexErrorDataCount: Optional[int] = None
    ChemIndexAssociationDataCount: Optional[int] = None
    ChemIndexExistDataCount: Optional[int] = None

    
class RequestReport(BaseModel):
    ImportRequestID: Optional[int] = None
    ImportTypeId: Optional[int] = None
    InputDataCount: Optional[int] = None
    NewDataCount: Optional[int] = None
    ExistsDataCount: Optional[int] = None
    ErrorDataCount: Optional[int] = None
    UpdateDataCount: Optional[int] = None
    DuplicateDataCount: Optional[int] = None
    ChemIndexObj: Optional[ChemIndexRequestReport] = None


class ReportFilterSchema(BaseModel):
    start_date: Optional[str] = Field(None, description="Start date in YYYY-MM-DD")
    end_date: Optional[str] = Field(None, description="End date in YYYY-MM-DD")
    week: Optional[int] = Field(None, description="ISO week number")
    supplier_id: Optional[int] = Field(None, description="Supplier ID")
    request_id: Optional[int] = Field(None, description="Filter by specific Request ID")
    request_group_id: Optional[str] = Field(None, description="Filter by Request Group ID")
    status: Optional[str] = Field(None, description="Filter by status")
    download: Optional[bool] = Field(False, description="Set True to download as Excel")
    filename: Optional[str] = Field(None, description="Custom filename for download")
