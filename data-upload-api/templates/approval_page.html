{% extends "base.html" %}

{% block title %}Approval Management{% endblock %}
{% block extra_head %}
<!-- Include shared confirmation dialog script -->
<script src="/static/js/shared-confirmation-dialog.js"></script>
<style>
    /* Modern Approval Page Styles */
    .approval-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        transition: box-shadow var(--transition);
    }

    .approval-container:hover {
        box-shadow: var(--shadow-md);
    }

    .approval-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--gray-200);
    }

    .header-content {
        display: flex;
        flex-direction: column;
    }

    .approval-header h2 {
        margin: 0;
        color: var(--gray-900);
        font-size: 1.75rem;
        display: flex;
        align-items: center;
    }

    .approval-header h2 i {
        margin-right: var(--spacing-sm);
        color: var(--primary);
    }

    .page-description {
        color: var(--gray-600);
        font-size: 1rem;
        margin-top: var(--spacing-xs);
        margin-left: 28px; /* Align with the header text after the icon */
    }

    .refresh-btn button {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background-color: var(--gray-100);
        color: var(--gray-800);
        border: 1px solid var(--gray-300);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
        transition: all var(--transition-fast);
    }

    .refresh-btn button:hover {
        background-color: var(--gray-200);
        transform: translateY(-2px);
    }

    /* Modern Tab Design */
    .approval-tabs {
        display: flex;
        margin-bottom: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        gap: var(--spacing-md);
    }

    .approval-tab {
        padding: var(--spacing-md) var(--spacing-lg);
        cursor: pointer;
        font-weight: var(--font-weight-bold);
        color: var(--gray-600);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        position: relative;
        margin-bottom: -1px;
    }

    .approval-tab i {
        font-size: 1rem;
    }

    .approval-tab.active {
        color: var(--primary);
        border-bottom: 3px solid var(--primary);
        background-color: rgba(67, 97, 238, 0.05);
    }

    .approval-tab:hover:not(.active) {
        color: var(--primary-dark);
        background-color: var(--gray-100);
    }

    .approval-tab .count {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 20px;
        height: 20px;
        padding: 0 var(--spacing-xs);
        border-radius: 10px;
        font-size: 0.75rem;
        font-weight: var(--font-weight-bold);
        margin-left: var(--spacing-xs);
    }

    .approval-tab[data-tab="pending"] .count {
        background-color: var(--warning);
        color: var(--gray-900);
    }

    .approval-tab[data-tab="approved"] .count {
        background-color: var(--success);
        color: white;
    }

    .approval-tab[data-tab="rejected"] .count {
        background-color: var(--danger);
        color: white;
    }

    /* Table Styles */
    .approval-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: var(--spacing-lg);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .approval-table th, .approval-table td {
        padding: var(--spacing-md);
        text-align: left;
        border-bottom: 1px solid var(--gray-200);
        vertical-align: middle;
    }

    .approval-table th {
        background-color: var(--gray-100);
        font-weight: var(--font-weight-bold);
        color: var(--gray-700);
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.5px;
        position: sticky;
        top: 0;
        z-index: 10;
        box-shadow: 0 1px 0 var(--gray-200);
    }

    .approval-table tr:last-child td {
        border-bottom: none;
    }

    .approval-table tbody tr {
        transition: background var(--transition-fast);
    }

    .approval-table tbody tr:hover {
        background-color: rgba(67, 97, 238, 0.05);
    }

    /* Column widths */
    .approval-table th:nth-child(1), .approval-table td:nth-child(1) { /* ID */
        width: 8%;
    }

    .approval-table th:nth-child(2), .approval-table td:nth-child(2) { /* File Name */
        width: 20%;
    }

    /* File name truncation */
    .approval-table td:nth-child(2) {
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: help;
    }

    .approval-table th:nth-child(3), .approval-table td:nth-child(3) { /* Request Type */
        width: 15%;
    }

    .approval-table th:nth-child(4), .approval-table td:nth-child(4) { /* Status */
        width: 10%;
    }

    .approval-table th:nth-child(5), .approval-table td:nth-child(5) { /* Reason */
        width: 20%;
    }

    .approval-table th:nth-child(6), .approval-table td:nth-child(6) { /* Date */
        width: 12%;
    }

    .approval-table th:nth-child(7), .approval-table td:nth-child(7) { /* Actions */
        width: 15%;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: var(--spacing-sm);
    }

    .approve-btn, .reject-btn, .view-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
        font-size: 0.85rem;
        cursor: pointer;
        transition: all var(--transition-fast);
        border: none;
        box-shadow: var(--shadow-sm);
    }

    .approve-btn {
        background-color: var(--success);
        color: white;
    }

    .reject-btn {
        background-color: var(--danger);
        color: white;
    }

    .view-btn {
        background-color: var(--secondary);
        color: var(--gray-900);
    }

    .approve-btn:hover, .reject-btn:hover, .view-btn:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .approve-btn:hover {
        background-color: #3d9140;
    }

    .reject-btn:hover {
        background-color: #d32f2f;
    }

    .view-btn:hover {
        background-color: #3db9dc;
    }

    /* Status Badges are now defined in global.css */

    /* Empty State */
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-xl) var(--spacing-lg);
        background-color: var(--gray-100);
        border-radius: var(--border-radius-lg);
        color: var(--gray-600);
        text-align: center;
        min-height: 200px;
    }

    .empty-state i {
        font-size: 3rem;
        margin-bottom: var(--spacing-md);
        color: var(--gray-400);
    }

    .empty-state p {
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    /* Modern Modal */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(3px);
        opacity: 0;
        transition: opacity var(--transition);
    }

    .modal.show {
        opacity: 1;
    }

    .modal-content {
        background-color: white;
        margin: 10% auto;
        padding: var(--spacing-lg);
        border-radius: var(--border-radius-lg);
        width: 450px;
        box-shadow: var(--shadow-lg);
        transform: translateY(-20px);
        opacity: 0;
        transition: all var(--transition);
    }

    .modal.show .modal-content {
        transform: translateY(0);
        opacity: 1;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--gray-200);
    }

    .modal-header h3 {
        margin: 0;
        color: var(--gray-900);
        font-size: 1.5rem;
    }

    .close-modal {
        font-size: 1.5rem;
        font-weight: var(--font-weight-bold);
        color: var(--gray-500);
        cursor: pointer;
        transition: color var(--transition-fast);
    }

    .close-modal:hover {
        color: var(--gray-800);
    }

    .modal-body {
        margin-bottom: var(--spacing-lg);
        color: var(--gray-700);
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-md);
    }

    /* Notification */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: var(--spacing-md) var(--spacing-lg);
        border-radius: var(--border-radius);
        color: white;
        font-weight: var(--font-weight-bold);
        z-index: 1001;
        opacity: 0;
        transform: translateY(-20px);
        transition: opacity var(--transition), transform var(--transition);
        box-shadow: var(--shadow-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .notification::before {
        font-family: "Font Awesome 6 Free";
        font-weight: 900;
        font-size: 1.2rem;
    }

    .notification.success {
        background-color: var(--success);
    }

    .notification.success::before {
        content: "\f00c";
    }

    .notification.error {
        background-color: var(--danger);
    }

    .notification.error::before {
        content: "\f00d";
    }

    .notification.show {
        opacity: 1;
        transform: translateY(0);
    }

    /* Tab Content Animation */
    .tab-content {
        opacity: 0;
        transform: translateY(10px);
        transition: opacity var(--transition-fast), transform var(--transition-fast);
    }

    .tab-content.active {
        opacity: 1;
        transform: translateY(0);
    }

    /* Loading Placeholder Animation */
    .loading-placeholder {
        display: inline-block;
        position: relative;
        color: var(--gray-400);
        background: linear-gradient(90deg, var(--gray-200), var(--gray-300), var(--gray-200));
        background-size: 200% 100%;
        animation: loading-animation 1.5s infinite;
        border-radius: var(--border-radius-sm);
        padding: 2px 8px;
    }

    @keyframes loading-animation {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Fade-in Animation for Content */
    .fade-in {
        animation: fade-in 0.5s ease-in-out;
    }

    @keyframes fade-in {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Text Utility Classes */
    .text-danger {
        color: var(--danger);
    }

    /* Multi-scenario Approval Styles */
    .approval-reasons.multi-scenario .approval-reasons-toggle {
        background-color: rgba(220, 53, 69, 0.15);
        color: var(--danger-color);
        font-weight: var(--font-weight-bold);
        border-left: 3px solid var(--danger-color);
    }

    .approval-reason-category {
        margin-bottom: var(--spacing-sm);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    .category-header {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.85rem;
        background-color: var(--gray-100);
        border-bottom: 1px solid var(--gray-200);
    }

    .supplier-category {
        color: var(--warning-color);
        border-left: 3px solid var(--warning-color);
    }

    .catalog-category {
        color: var(--danger-color);
        border-left: 3px solid var(--danger-color);
    }

    .property-category {
        color: var(--info-color);
        border-left: 3px solid var(--info-color);
    }

    .warehouse-category {
        color: var(--warning-color);
        border-left: 3px solid var(--warning-color);
    }

    .unit-category {
        color: var(--warning-color);
        border-left: 3px solid var(--warning-color);
    }

    .category-reasons {
        background-color: white;
    }

    /* Approval Reasons List in Confirmation Dialog */
    .approval-reasons-list.categorized {
        border: none;
        background: none;
        padding: 0;
    }

    .approval-reasons-list.categorized .category-header {
        margin-top: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-sm);
        background-color: var(--gray-100);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        border-bottom: 1px solid var(--gray-200);
    }

    /* Reason Badge Styles */
    .reason-badge {
        display: inline-flex;
        align-items: center;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius);
        font-size: 0.85rem;
        font-weight: var(--font-weight-medium);
        background-color: var(--gray-100);
        color: var(--gray-800);
        box-shadow: var(--shadow-sm);
        position: relative;
    }

    .reason-badge i {
        margin-right: var(--spacing-xs);
        font-size: 0.9rem;
    }

    .reason-approved {
        background-color: var(--success-light);
        color: var(--success-color);
        border-left: 3px solid var(--success-color);
    }

    .reason-rejected {
        background-color: var(--danger-light);
        color: var(--danger-color);
        border-left: 3px solid var(--danger-color);
    }

    /* Approval Reasons Styles */
    .approval-reasons {
        margin-top: var(--spacing-xs);
    }

    .approval-reason {
        display: flex;
        align-items: center;
        margin-bottom: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius);
        font-size: 0.8rem;
        background-color: var(--gray-50);
        border-left: 3px solid var(--primary);
    }

    .approval-reason i {
        margin-right: var(--spacing-xs);
        font-size: 0.9rem;
    }

    .approval-reason.supplier-reason {
        border-left-color: var(--warning);
    }

    .approval-reason.supplier-reason i {
        color: var(--warning);
    }

    .approval-reason.catalog-reason {
        border-left-color: var(--danger);
    }

    .approval-reason.catalog-reason i {
        color: var(--danger);
    }

    .approval-reason.property-reason {
        border-left-color: var(--info);
    }

    .approval-reason.property-reason i {
        color: var(--info);
    }

    .approval-reason.warehouse-reason {
        border-left-color: var(--warning);
    }

    .approval-reason.warehouse-reason i {
        color: var(--warning);
    }

    .approval-reason.unit-reason {
        border-left-color: var(--warning);
    }

    .approval-reason.unit-reason i {
        color: var(--warning);
    }

    .approval-reasons-toggle {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--border-radius);
        font-size: 0.75rem;
        font-weight: var(--font-weight-bold);
        background-color: var(--primary-light);
        color: var(--primary);
        cursor: pointer;
        margin-top: var(--spacing-xs);
    }

    .approval-reasons-toggle i {
        margin-right: var(--spacing-xs);
    }

    .approval-reasons-container {
        display: none;
        margin-top: var(--spacing-xs);
    }

    .approval-reasons-container.show {
        display: block;
    }

    /* Tooltip for validation details */
    .validation-tooltip {
        position: absolute;
        z-index: 1000; /* Higher z-index to ensure it's above other elements */
        width: 220px;
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-md);
        padding: var(--spacing-sm);
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease, visibility 0.2s ease;
        pointer-events: none;
    }

    /* Default position (below the badge) */
    .validation-tooltip.tooltip-bottom {
        top: 100%;
        left: 0;
        margin-top: 8px;
    }

    /* Position for bottom rows (above the badge) */
    .validation-tooltip.tooltip-top {
        bottom: 100%;
        left: 0;
        margin-bottom: 8px;
    }

    .reason-badge:hover .validation-tooltip {
        opacity: 1;
        visibility: visible;
    }

    /* Arrow for tooltip pointing up (for bottom position) */
    .validation-tooltip.tooltip-bottom:before {
        content: '';
        position: absolute;
        top: -6px;
        left: 20px;
        width: 12px;
        height: 12px;
        background-color: white;
        transform: rotate(45deg);
        box-shadow: -2px -2px 5px rgba(0,0,0,0.05);
    }

    /* Arrow for tooltip pointing down (for top position) */
    .validation-tooltip.tooltip-top:before {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 20px;
        width: 12px;
        height: 12px;
        background-color: white;
        transform: rotate(45deg);
        box-shadow: 2px 2px 5px rgba(0,0,0,0.05);
    }

    .validation-details {
        font-size: 0.8rem;
        color: var(--gray-700);
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-xs);
    }

    .validation-details div {
        padding: 4px 8px;
        border-radius: 4px;
        background-color: var(--gray-50);
        display: flex;
        justify-content: space-between;
    }

    .validation-details div:before {
        content: attr(data-label);
        font-weight: var(--font-weight-medium);
    }
</style>
{% endblock %}

{% block content %}
<div class="approval-container">
    <div class="approval-header">
        <div class="header-content">
            <h2><i class="fas fa-check-circle"></i> Approval Management</h2>
            <div class="page-description">Review and approve pending data import requests</div>
        </div>
        <div class="refresh-btn">
            <button onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
        </div>
    </div>

    <div class="approval-tabs">
        <div class="approval-tab active" data-tab="pending">
            <i class="fas fa-hourglass-half"></i> Pending Requests
            <span class="count">0</span>
        </div>
        <div class="approval-tab" data-tab="approved">
            <i class="fas fa-check"></i> Approved Requests
            <span class="count">0</span>
        </div>
        <div class="approval-tab" data-tab="rejected">
            <i class="fas fa-times"></i> Rejected Requests
            <span class="count">0</span>
        </div>

        <div style="display: flex; justify-content: flex-end; align-items: center; margin-bottom: 10px; gap: 8px;">
            <button id="processSelectedApproveBtn" style="display: none;" data-action="approve">
              <i class="fas fa-check"></i> Approve
            </button>
            <button id="processSelectedRejectBtn" style="display: none;" data-action="reject">
              <i class="fas fa-times"></i> Reject
            </button>
          </div>  

    </div>

    <div id="pending-tab" class="tab-content active">
        <table class="approval-table" id="pending-table">
            <thead>
                <tr>
                    <th><input type="checkbox" id="selectAllCheckbox" /></th>
                    <th>Request ID</th>
                    <th>File Name</th>
                    <th>Request Type</th>
                    <th>Status</th>
                    <th>Reason</th>
                    <th>Created Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="pending-requests-body">
                <!-- Pending requests will be loaded here -->
            </tbody>
        </table>
        <div id="pending-empty" class="empty-state" style="display: none;">
            <i class="fas fa-inbox"></i>
            <p>No pending requests found</p>
        </div>
    </div>

    <div id="approved-tab" class="tab-content" style="display: none;">
        <table class="approval-table" id="approved-table">
            <thead>
                <tr>
                    <th>Request ID</th>
                    <th>File Name</th>
                    <th>Request Type</th>
                    <th>Status</th>
                    <th>Reason</th>
                    <th>Approved Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="approved-requests-body">
                <!-- Approved requests will be loaded here -->
            </tbody>
        </table>
        <div id="approved-empty" class="empty-state" style="display: none;">
            <i class="fas fa-check-circle"></i>
            <p>No approved requests found</p>
        </div>
    </div>

    <div id="rejected-tab" class="tab-content" style="display: none;">
        <table class="approval-table" id="rejected-table">
            <thead>
                <tr>
                    <th>Request ID</th>
                    <th>File Name</th>
                    <th>Request Type</th>
                    <th>Status</th>
                    <th>Reason</th>
                    <th>Rejected Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="rejected-requests-body">
                <!-- Rejected requests will be loaded here -->
            </tbody>
        </table>
        <div id="rejected-empty" class="empty-state" style="display: none;">
            <i class="fas fa-times-circle"></i>
            <p>No rejected requests found</p>
        </div>
    </div>
</div>

<!-- We're now using the shared confirmation dialog from shared-confirmation-dialog.js -->

<!-- Notification -->
<div id="notification" class="notification">
    <span id="notification-message"></span>
</div>

{% endblock %}

{% block extra_scripts %}
<script src="/static/js/approval.js"></script>
{% endblock %}
