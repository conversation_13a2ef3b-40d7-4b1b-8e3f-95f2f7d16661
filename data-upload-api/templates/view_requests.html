{% extends "base.html" %}

{% block title %}Request List{% endblock %}
{% block extra_head %}
<style>
    /* Modern Request List Styles */
    .request-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        transition: box-shadow var(--transition);
    }

    .request-container:hover {
        box-shadow: var(--shadow-md);
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-md);
        border-bottom: 1px solid var(--gray-200);
    }

    .header-content {
        display: flex;
        flex-direction: column;
    }

    .page-header h2 {
        margin: 0;
        color: var(--gray-900);
        font-size: 1.75rem;
        display: flex;
        align-items: center;
    }

    .page-header h2 i {
        margin-right: var(--spacing-sm);
        color: var(--primary);
    }

    .page-description {
        color: var(--gray-600);
        font-size: 1rem;
        margin-top: var(--spacing-xs);
        margin-left: 28px; /* Align with the header text after the icon */
    }

    .refresh-btn button {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        background-color: var(--gray-100);
        color: var(--gray-800);
        border: 1px solid var(--gray-300);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
        transition: all var(--transition-fast);
    }

    .refresh-btn button:hover {
        background-color: var(--gray-200);
        transform: translateY(-2px);
    }

    /* Stats Cards Styles */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .stat-card {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        padding: var(--spacing-md);
        display: flex;
        align-items: center;
        /* transition: all var(--transition-fast); */
        border: 1px solid var(--gray-200);
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: var(--shadow-md);
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: var(--spacing-md);
        font-size: 1.25rem;
        color: white;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Added subtle shadow for better visibility */
    }

    .stat-icon.total {
        background-color: #4361ee; /* Blue color for document icon */
    }

    .stat-icon.completed {
        background-color: #4caf50; /* Green color for check icon */
    }

    .stat-icon.pending {
        background-color: #ffc107; /* Yellow color for clock icon */
    }

    .stat-icon.today {
        background-color: #4cc9f0; /* Light blue color for calendar icon */
    }

    .stat-content {
        flex: 1;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: var(--font-weight-bold);
        color: var(--gray-900);
        line-height: 1.2;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--gray-600);
        margin-top: 2px;
    }

    /* Modern Tab Design */
    .tabs {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        padding-bottom: var(--spacing-md);
    }

    .tabs-left {
        display: flex;
        gap: var(--spacing-md);
        align-items: center;
    }

    .tabs-right {
        display: flex;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .tab {
        padding: var(--spacing-md) var(--spacing-lg);
        cursor: pointer;
        font-weight: var(--font-weight-bold);
        color: var(--gray-600);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        position: relative;
        margin-bottom: -1px;
        background-color: transparent;
        border: none;
    }

    .tab i {
        font-size: 1rem;
    }

    .tab.active {
        color: var(--primary);
        border-bottom: 3px solid var(--primary);
        background-color: rgba(67, 97, 238, 0.05);
    }

    .tab:hover:not(.active) {
        color: var(--primary-dark);
        background-color: var(--gray-100);
        transform: translateY(-2px);
    }

    /* Action Buttons in Tab Area */
    .action-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        border: none;
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: 0.875rem;
        box-shadow: var(--shadow-sm);
    }

    .custom-report-btn {
        background-color: rgba(76, 201, 240, 0.1);
        color: var(--secondary);
        border: 1px solid rgba(76, 201, 240, 0.3);
    }

    .custom-report-btn:hover {
        background-color: var(--secondary);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 201, 240, 0.3);
    }

    .approve-btn {
        background-color: rgba(76, 175, 80, 0.1);
        color: var(--success);
        border: 1px solid rgba(76, 175, 80, 0.3);
    }

    .approve-btn:hover {
        background-color: var(--success);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    .reject-btn {
        background-color: rgba(244, 67, 54, 0.1);
        color: var(--danger);
        border: 1px solid rgba(244, 67, 54, 0.3);
    }

    .reject-btn:hover {
        background-color: var(--danger);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
    }

    /* Custom Report Modal Styles */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        backdrop-filter: blur(4px);
    }

    .modal-overlay.active {
        display: flex;
        animation: fadeIn 0.3s ease;
    }

    .modal-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-lg);
        width: 90%;
        max-width: 700px;
        max-height: 90vh;
        overflow: hidden;
        animation: slideIn 0.3s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    @keyframes slideIn {
        from { transform: translateY(-50px) scale(0.95); opacity: 0; }
        to { transform: translateY(0) scale(1); opacity: 1; }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--gray-200);
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        color: white;
    }

    .modal-header h3 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 1.25rem;
    }

    .modal-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.25rem;
        cursor: pointer;
        padding: var(--spacing-xs);
        border-radius: var(--border-radius);
        transition: all var(--transition-fast);
    }

    .modal-close:hover {
        background-color: rgba(255, 255, 255, 0.1);
        transform: scale(1.1);
    }

    .modal-body {
        padding: var(--spacing-lg);
        max-height: 60vh;
        overflow-y: auto;
    }

    .filter-section {
        margin-bottom: var(--spacing-xl);
    }

    .filter-section h4 {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
        color: var(--gray-700);
        font-size: 1.1rem;
        border-bottom: 2px solid var(--gray-200);
        padding-bottom: var(--spacing-xs);
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .filter-row {
        display: flex;
        gap: var(--spacing-lg);
        flex-wrap: wrap;
    }

    .radio-label {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        cursor: pointer;
        font-weight: var(--font-weight-normal);
        transition: all var(--transition-fast);
    }

    .radio-label:hover {
        color: var(--primary);
    }

    .radio-label input[type="radio"] {
        display: none;
    }

    .radio-custom {
        width: 18px;
        height: 18px;
        border: 2px solid var(--gray-400);
        border-radius: 50%;
        position: relative;
        transition: all var(--transition-fast);
    }

    .radio-label input[type="radio"]:checked + .radio-custom {
        border-color: var(--primary);
        background-color: var(--primary);
    }

    .radio-label input[type="radio"]:checked + .radio-custom::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 6px;
        background-color: white;
        border-radius: 50%;
    }

    .custom-date-range {
        margin-top: var(--spacing-md);
        padding: var(--spacing-md);
        background-color: var(--gray-100);
        border-radius: var(--border-radius);
        border-left: 4px solid var(--primary);
    }

    .date-inputs {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }

    .filter-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-md);
    }

    .input-group {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .input-group label {
        font-weight: var(--font-weight-bold);
        color: var(--gray-700);
        font-size: 0.9rem;
    }

    .input-group input,
    .input-group select {
        padding: var(--spacing-sm);
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        font-size: 0.9rem;
        transition: all var(--transition-fast);
        background-color: white;
    }

    .input-group input:focus,
    .input-group select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-md);
        padding: var(--spacing-lg);
        border-top: 1px solid var(--gray-200);
        background-color: var(--gray-50);
    }

    .btn-secondary {
        background-color: var(--gray-500);
        color: white;
        border: none;
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .btn-secondary:hover {
        background-color: var(--gray-600);
        transform: translateY(-2px);
    }

    .btn-primary {
        background-color: var(--primary);
        color: white;
        border: none;
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--border-radius);
        font-weight: var(--font-weight-bold);
        cursor: pointer;
        transition: all var(--transition-fast);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    .btn-primary:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
    }

    .btn-primary:disabled {
        background-color: var(--gray-400);
        cursor: not-allowed;
        transform: none;
    }

    /* Table Wrapper */
    .table-wrapper {
        overflow-x: auto;
        border-radius: var(--border-radius);
        margin-top: var(--spacing-lg);
    }

    /* Modern DataTable Styling */
    table.dataTable {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        border: none;
        margin-top: var(--spacing-md) !important;
        margin-bottom: var(--spacing-md) !important;
    }

    table.dataTable thead th {
        background-color: var(--gray-100);
        color: var(--gray-800);
        font-weight: var(--font-weight-bold);
        padding: var(--spacing-md) var(--spacing-md);
        border-bottom: 2px solid var(--gray-300);
        text-align: left;
        vertical-align: middle;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    table.dataTable tbody td {
        padding: var(--spacing-md);
        vertical-align: middle;
        border-bottom: 1px solid var(--gray-200);
    }

    table.dataTable tbody tr:last-child td {
        border-bottom: none;
    }

    table.dataTable tbody tr:hover {
        background-color: rgba(67, 97, 238, 0.03);
    }

    /* DataTable Controls Styling */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter {
        margin-bottom: var(--spacing-md);
    }

    .dataTables_wrapper .dataTables_length select {
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        padding: var(--spacing-xs) var(--spacing-sm);
        margin: 0 var(--spacing-xs);
        background-color: white;
    }

    .dataTables_wrapper .dataTables_filter input {
        border: 1px solid var(--gray-300);
        border-radius: var(--border-radius);
        padding: var(--spacing-xs) var(--spacing-sm);
        transition: all var(--transition-fast);
        min-width: 200px;
        background-color: white;
    }

    .dataTables_wrapper .dataTables_filter input:focus {
        border-color: var(--primary);
        box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        outline: none;
    }

    .dataTables_wrapper .dataTables_info {
        padding-top: var(--spacing-md);
        color: var(--gray-600);
        font-size: 0.9rem;
    }

    .dataTables_wrapper .dataTables_paginate {
        padding-top: var(--spacing-md);
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: var(--spacing-xs) var(--spacing-sm);
        margin: 0 2px;
        border-radius: var(--border-radius);
        border: 1px solid var(--gray-300);
        background: white;
        color: var(--gray-700) !important;
        cursor: pointer;
        transition: none; /* Remove any transitions that might cause movement */
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: var(--primary);
        color: white !important;
        border-color: var(--primary);
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current) {
        background: var(--gray-100);
        color: var(--primary) !important;
        border-color: var(--primary-light);
        /* Removed transform to prevent movement */
    }

    /* Action Icons Container */
    .action-buttons-container {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    /* Base Action Icon Styles */
    .eye-icon, .download-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        cursor: pointer;
        transition: all var(--transition-fast);
        position: relative;
        overflow: hidden;
    }

    /* View Icon Styles */
    .eye-icon {
        background-color: rgba(67, 97, 238, 0.1);
        color: var(--primary);
    }

    .eye-icon:hover {
        background-color: var(--primary);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
    }

    /* Download Icon Styles */
    .download-icon {
        background-color: rgba(76, 175, 80, 0.1);
        color: #4caf50;
    }

    .download-icon:hover {
        background-color: #4caf50;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    /* Ripple Effect for Action Icons */
    .eye-icon::before, .download-icon::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
    }

    .eye-icon:active::before, .download-icon:active::before {
        width: 100%;
        height: 100%;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .stats-container {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 992px) {
        .table-container {
            padding: var(--spacing-md);
        }

        .tab {
            padding: var(--spacing-sm) var(--spacing-md);
            font-size: 0.9rem;
        }

        .dataTables_wrapper .dataTables_filter input {
            min-width: 150px;
        }
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-md);
        }

        .header-content {
            width: 100%;
        }

        .refresh-btn {
            align-self: flex-end;
            margin-top: var(--spacing-md);
        }

        .tabs {
            flex-direction: column;
            gap: var(--spacing-md);
            align-items: stretch;
        }

        .tabs-left, .tabs-right {
            justify-content: center;
        }

        .tabs-right {
            flex-wrap: wrap;
        }

        .modal-container {
            width: 95%;
            max-height: 95vh;
        }

        .filter-row {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .date-inputs {
            grid-template-columns: 1fr;
        }

        .filter-grid {
            grid-template-columns: 1fr;
        }

        .modal-footer {
            flex-direction: column;
            gap: var(--spacing-sm);
        }

        .radio-group {
            grid-template-columns: 1fr;
            gap: var(--spacing-xs);
        }

        .radio-option {
            font-size: 14px;
            padding: 8px 12px;
        }

        .action-btn {
            font-size: 12px;
            padding: 8px 12px;
        }
    }

    @media (max-width: 480px) {
        .modal-container {
            width: 98%;
            margin: 5px auto;
        }

        .modal-header h3 {
            font-size: 18px;
        }

        .filter-group input, .filter-group select {
            font-size: 16px; /* Prevent zoom on iOS */
        }

        .btn {
            font-size: 14px;
            padding: 10px 16px;
        }

        .stats-container {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="request-container">
    <div class="page-header">
        <div class="header-content">
            <h2><i class="fas fa-list-ul"></i> Request List</h2>
            <div class="page-description">View and manage all data import requests</div>
        </div>
        <div class="refresh-btn">
            <button onclick="refreshData()">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </button>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tabs">
        <!-- Left side: Navigation tabs -->
        <div class="tabs-left">
            <div class="tab active" id="openTab">
                <i class="fas fa-folder-open"></i> Open Requests
            </div>
            <div class="tab" id="archiveTab">
                <i class="fas fa-archive"></i> Archived Requests
            </div>
        </div>

        <!-- Right side: Action buttons -->
        <div class="tabs-right">
            <button id="customReportBtn" class="action-btn custom-report-btn">
                <i class="fas fa-chart-line"></i> Custom Report
            </button>
            <button id="processSelectedApproveBtn" class="action-btn approve-btn" style="display: none;" data-action="approve">
                <i class="fas fa-check"></i> Approve
            </button>
            <button id="processSelectedRejectBtn" class="action-btn reject-btn" style="display: none;" data-action="reject">
                <i class="fas fa-times"></i> Reject
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-container">
        <div class="stat-card">
            <div class="stat-icon total">
                <i class="fa-solid fa-file-import"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="totalRequests" data-target="0">0</div>
                <div class="stat-label">Total Requests</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon completed">
                <i class="fas fa-check"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="completedRequests" data-target="0">0</div>
                <div class="stat-label">Completed</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon pending">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="pendingRequests" data-target="0">0</div>
                <div class="stat-label">Pending</div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon today">
                <i class="fas fa-calendar-day"></i>
            </div>
            <div class="stat-content">
                <div class="stat-value" id="todayRequests" data-target="0">0</div>
                <div class="stat-label">Today's Requests</div>
            </div>
        </div>
    </div>

    <!-- Custom Report Filter Modal -->
    <div id="customReportModal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <h3><i class="fas fa-filter"></i> Custom Report Filters</h3>
                <button class="modal-close" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <form id="customReportForm">
                    <!-- Time-based Filters -->
                    <div class="filter-section">
                        <h4><i class="fas fa-calendar-alt"></i> Time Period</h4>
                        <div class="filter-group">
                            <div class="filter-row">
                                <label class="radio-label">
                                    <input type="radio" name="timeFilter" value="this_week" checked>
                                    <span class="radio-custom"></span>
                                    This Week
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="timeFilter" value="this_month">
                                    <span class="radio-custom"></span>
                                    This Month
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="timeFilter" value="last_month">
                                    <span class="radio-custom"></span>
                                    Last Month
                                </label>
                            </div>
                            <div class="filter-row">
                                <label class="radio-label">
                                    <input type="radio" name="timeFilter" value="this_year">
                                    <span class="radio-custom"></span>
                                    This Year
                                </label>
                                <label class="radio-label">
                                    <input type="radio" name="timeFilter" value="custom">
                                    <span class="radio-custom"></span>
                                    Custom Range
                                </label>
                            </div>
                        </div>

                        <!-- Custom Date Range -->
                        <div id="customDateRange" class="custom-date-range" style="display: none;">
                            <div class="date-inputs">
                                <div class="input-group">
                                    <label>Start Date</label>
                                    <input type="date" id="startDate" name="startDate">
                                </div>
                                <div class="input-group">
                                    <label>End Date</label>
                                    <input type="date" id="endDate" name="endDate">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Entity Filters -->
                    <div class="filter-section">
                        <h4><i class="fas fa-filter"></i> Filter Criteria</h4>
                        <div class="filter-grid">
                            <div class="input-group">
                                <label for="supplierIdFilter">Supplier ID</label>
                                <input type="number" id="supplierIdFilter" name="supplierId" placeholder="Enter Supplier ID">
                            </div>
                            <div class="input-group">
                                <label for="requestIdFilter">Request ID</label>
                                <input type="number" id="requestIdFilter" name="requestId" placeholder="Enter Request ID">
                            </div>
                            <div class="input-group">
                                <label for="requestGroupIdFilter">Request Group ID</label>
                                <input type="text" id="requestGroupIdFilter" name="requestGroupId" placeholder="Enter Request Group ID">
                            </div>
                            <div class="input-group">
                                <label for="statusFilter">Status</label>
                                <select id="statusFilter" name="status">
                                    <option value="">All Statuses</option>
                                    <option value="Open">Open</option>
                                    <option value="Start">Start</option>
                                    <option value="Inprogress">In Progress</option>
                                    <option value="Done">Done</option>
                                    <option value="Error">Error</option>
                                    <option value="Pending">Pending</option>
                                    <option value="Approved">Approved</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn-secondary" id="resetFilters">
                    <i class="fas fa-undo"></i> Reset Filters
                </button>
                <button type="button" class="btn-primary" id="applyFilters">
                    <i class="fas fa-download"></i> Generate Report
                </button>
            </div>
        </div>
    </div>

    <!-- Table Wrapper with contained loader -->
    <div class="table-container">
        <!-- Table-specific loader that's contained within the table area -->
        <div class="table-loader-overlay">
            <div class="table-loader"></div>
        </div>

        <div class="table-wrapper">
            <table id="requestsTable" class="display" style="width: 100%">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAllCheckbox" /></th>
                        <th>Request Group ID</th>
                        <th>Request ID</th>
                        <th>File Name</th>
                        <th>Request Type</th>
                        <th>Stage</th>
                        <th>Status</th>
                        <th>Created Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Dynamic content will be populated here -->
                </tbody>
            </table>
        </div>
    </div>
</div>

    {% block extra_scripts %}

    <script>
        $(document).ready(function() {
            let table;
            
            function toggleProcessButton() {
                const hasSelected = $('.row-checkbox:checked').length > 0;
                $('#processSelectedApproveBtn').toggle(hasSelected);
                $('#processSelectedRejectBtn').toggle(hasSelected);
            }
            
            function getSelectedRequestIds() {
                const selectedIds = [];
                $('.row-checkbox:checked').each(function () {
                    selectedIds.push($(this).attr('data-id'));
                });
                return selectedIds;
            }
            
            // Common handler for both buttons
            async function handleRequestAction(actionType) {
                const requestIds = getSelectedRequestIds();
            
                if (requestIds.length === 0) {
                    alert('Please select at least one request.');
                    return;
                }
            
                const approved = actionType === 'approve';
                const baseUrl = window.location.origin;
            
                try {
                    const updatePromises = requestIds.map(async requestId => {
                        const response = await fetch(`${baseUrl}/api/requests/${requestId}/status`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({ approved: approved })  // ✅ Send boolean only
                        });
            
                        const result = await response.json();
            
                        if (!response.ok) {
                            throw new Error(result?.detail || `Failed to ${approved ? 'approve' : 'reject'} request ${requestId}`);
                        }
            
                        return result;
                    });
            
                    // Wait for all updates to finish
                    await Promise.all(updatePromises);
            
                    // ✅ Show success message
                    showPopupMessage(`Requests ${approved ? 'approved' : 'rejected'} successfully.`, 'success');
            
                    // 🔁 Refresh after 5 seconds
                    setTimeout(() => {
                        window.location.reload();
                    }, 5000);
            
                } catch (error) {
                    console.error('Bulk update error:', error);
                    showPopupMessage(`Error: ${error.message}`, 'error');
            
                    // 🔁 Optionally refresh after 5 sec on error
                    setTimeout(() => {
                        window.location.reload();
                    }, 5000);
                }
            }

            function showPopupMessage(message, type) {
                const popup = document.createElement('div');
                popup.textContent = message;
                popup.style.position = 'fixed';
                popup.style.top = '20px';
                popup.style.right = '20px';
                popup.style.zIndex = 1000;
                popup.style.padding = '12px 20px';
                popup.style.borderRadius = '8px';
                popup.style.color = '#fff';
                popup.style.fontWeight = 'bold';
                popup.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                popup.style.backgroundColor = type === 'success' ? '#28a745' : '#dc3545';
            
                document.body.appendChild(popup);
            
                setTimeout(() => {
                    popup.remove();
                }, 4000); // Popup disappears after 4 seconds
            }
            
            // Attach handler to both buttons
            $('#processSelectedApproveBtn, #processSelectedRejectBtn').on('click', function () {
                const actionType = $(this).data('action'); // 'approve' or 'reject'
                handleRequestAction(actionType);
            });
            $('.table-container').on('change', '.row-checkbox', function() {
                toggleProcessButton();
            });

            $('#selectAllCheckbox').on('change', function () {
                $('.row-checkbox').prop('checked', $(this).is(':checked'));
                toggleProcessButton();
            });
            
            function getSelectedRequestIds() {
                const selectedIds = [];
                $('.row-checkbox:checked').each(function() {
                    selectedIds.push($(this).attr('data-id'));
                });
                return selectedIds;
            }
            
            // Function to update stats cards with real data
            function updateStatsCards() {
                // Fetch real counts from the API
                fetch('/api/request-counts')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to fetch request counts');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Update data-target attributes with real values
                        $('#totalRequests').attr('data-target', data.total);
                        $('#completedRequests').attr('data-target', data.completed);
                        $('#pendingRequests').attr('data-target', data.pending);
                        $('#todayRequests').attr('data-target', data.today);

                        // Reset counter values to 0 before animating
                        $('.stat-value').text('0');

                        // Animate counters with real data
                        animateAllCounters();
                    })
                    .catch(error => {
                        console.error('Error fetching request counts:', error);
                        // Fallback to animation with existing values if fetch fails
                        animateAllCounters();
                    });
            }

            // Function to animate counting up - optimized for all number sizes
            function animateCounter(element, target, duration) {
                let progress = 0;

                // Fixed frame rate for all counters
                const fps = 30;
                const totalFrames = duration / 1000 * fps;
                const frameDuration = 1000 / fps;

                // Calculate step size based on target value
                // This ensures larger numbers increment in larger steps
                const stepSize = Math.max(1, Math.floor(target / 30));

                // Start value
                let currentValue = 0;
                element.text(currentValue);

                const timer = setInterval(() => {
                    progress += 1 / totalFrames;

                    // Use easeOutExpo for all numbers - fast start with smooth ending
                    // This works well for both small and large numbers
                    const easeOutValue = progress === 1 ? 1 : 1 - Math.pow(2, -10 * progress);

                    // Calculate new value
                    const newValue = Math.floor(target * easeOutValue);

                    // Only update if value has changed by at least stepSize or we're at the end
                    if (newValue >= target || newValue >= currentValue + stepSize) {
                        currentValue = newValue > target ? target : newValue;
                        element.text(currentValue);
                    }

                    // End animation when we reach the target
                    if (progress >= 1 || currentValue >= target) {
                        clearInterval(timer);
                        element.text(target);
                    }
                }, frameDuration);
            }

            // Animate all stat counters on page load - all at the same time
            function animateAllCounters() {
                $('.stat-value').each(function() {
                    const $this = $(this);
                    const target = parseInt($this.attr('data-target'));

                    // All counters start at the same time - no staggering
                    // Fixed duration for all counters regardless of size
                    const fixedDuration = 800; // 800ms for all counters
                    animateCounter($this, target, fixedDuration);
                });
            }

            function initializeDataTable() {
                // Show table-specific loader
                showTableLoader();

                if ($.fn.DataTable.isDataTable("#requestsTable")) {
                    table.destroy();
                    $("#requestsTable").find("tbody").empty(); // Clear the table before reinitializing
                }

                table = $('#requestsTable').DataTable({
                    processing: false, // Disable DataTables built-in processing indicator
                    serverSide: true,
                    responsive: true, // Enable responsive behavior
                    ajax: {
                        url: "/requests",  // Your backend API endpoint
                        data: function(d) {
                            d.skip = d.start;  // Pagination start index
                            d.limit = d.length; // Page size
                            d.search_value = d.search.value;  // Search term
                            d.sort_column = d.order[0] ? d.columns[d.order[0].column].data : 'createdDate';  // Sorting column
                            d.sort_order = d.order[0] ? d.order[0].dir : 'desc';  // Default to newest first
                            d.active_tab = $('.tab.active').attr('id');
                            d.include_stats = true; // Request stats data
                        },
                        beforeSend: function() {
                            // Show table-specific loader for all AJAX requests
                            showTableLoader();
                        },
                        complete: function() {
                            // Hide table-specific loader when request completes
                            hideTableLoader();
                        },
                        dataSrc: function (json) {
                            // We don't need to update stats here as we're fetching them separately

                            return json.data.map(function (request) {
                                // Use the common status badge component
                                const statusBadge = createStatusBadge(request.StatusObj.StatusName);

                                // Format the date nicely
                                const date = new Date(request.CreatedDate);
                                const formattedDate = date.toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                });

                                const formattedTime = date.toLocaleTimeString('en-US', {
                                    hour: '2-digit',
                                    minute: '2-digit'
                                });

                                const dateDisplay = `<div>${formattedDate}</div><div class="text-muted small">${formattedTime}</div>`;

                                // Create action buttons - download button only for Done/Completed status
                                const statusName = request.StatusObj.StatusName.toLowerCase();
                                const showDownloadButton = statusName === 'done' || statusName === 'completed';

                                let actionButtons = `<div class="action-buttons-container">
                                    <span class="eye-icon" data-id="${request.requestTypeId}" title="View Request">
                                        <i class="fas fa-eye"></i>
                                    </span>`;

                                if (showDownloadButton) {
                                    actionButtons += `<span class="download-icon" data-id="${request.requestTypeId}" title="Download Report">
                                        <i class="fas fa-download"></i>
                                    </span>`;
                                }

                                actionButtons += `</div>`;

                                return {
                                    request_group_id: request.request_group_id ? request.request_group_id : "-",
                                    requestId: `<strong>${request.requestTypeId}</strong>`,
                                    fileName: `<div class="file-name" title="${request.InputFileName}">${request.InputFileName}</div>`,
                                    requestType: request.RequestTypeObj.RequestTypeName,
                                    stage: `<span class="stage-badge">${request.StageObj.StageName}</span>`,
                                    status: statusBadge,
                                    createdDate: dateDisplay,
                                    viewAction: actionButtons
                                };
                            });
                        },
                        error: function(xhr, error, thrown) {
                            console.error("Error loading data:", error);
                            showNotification("There was an error loading the data. Please try again.", "error");
                            hideLoader();
                        }
                    },
                    columns: [
                        {
                            data: null,
                            orderable: false,
                            className: 'text-center',
                            width: "5%",
                            render: function(data, type, row) {
                                return `<input type="checkbox" class="row-checkbox" data-id="${data.requestId.replace(/<[^>]*>?/gm, '')}">`;
                            }
                        },
                        { data: 'request_group_id', width: "8%" },
                        { data: 'requestId', width: "8%" },
                        { data: 'fileName', width: "20%" },
                        { data: 'requestType', width: "15%" },
                        { data: 'stage', width: "12%" },
                        { data: 'status', width: "12%" },
                        { data: 'createdDate', width: "15%" },
                        { data: 'viewAction', title: 'Actions', orderable: false, width: "12%", className: 'text-center' }
                    ],
                    order: [[6, 'desc']], // Sort by created date by default (newest first)
                    paging: true,
                    pageLength: 10,
                    stateSave: true,
                    lengthMenu: [5, 10, 25, 50],
                    language: {
                        lengthMenu: "Show _MENU_ entries",
                        zeroRecords: "No requests found",
                        info: "Showing _START_ to _END_ of _TOTAL_ requests",
                        infoEmpty: "No requests available",
                        infoFiltered: "(filtered from _MAX_ total requests)",
                        search: "Search requests:"
                    },
                    pagingType: 'simple_numbers', // Changed to simple_numbers for cleaner pagination
                    createdRow: function(row, data, dataIndex) {
                        // Add hover effect to rows
                        $(row).addClass('table-row-hover');
                    },
                    drawCallback: function() {
                        // Add tooltips to long file names
                        $('.file-name').each(function() {
                            if(this.offsetWidth < this.scrollWidth) {
                                $(this).attr('title', $(this).text());
                            }
                        });
                    }
                });
            }

            // Initialize DataTable on page load
            initializeDataTable();

            // Fetch and display real counts
            updateStatsCards();

            // Tab switching functionality with animation
            $('.tab').click(function() {
                $('.tab').removeClass('active');
                $(this).addClass('active');

                // Show table loader immediately
                showTableLoader();

                // Update stats with real data
                updateStatsCards();

                // Reinitialize DataTable with the new tab filter
                initializeDataTable();
            });

            // View Request Click
            $('#requestsTable').on('click', '.eye-icon', function() {
                const requestId = $(this).data('id');

                // Add click animation
                $(this).addClass('clicked');
                setTimeout(() => {
                    window.location.href = "/request_detail/" + requestId;
                }, 200);
            });

            // Download Report Click
            $('#requestsTable').on('click', '.download-icon', function() {
                const requestId = $(this).data('id');

                // Add click animation
                $(this).addClass('clicked');

                // Show loading state
                const originalIcon = $(this).find('i');
                originalIcon.removeClass('fa-download').addClass('fa-spinner fa-spin');

                // Call download function
                downloadReport(requestId, $(this));
            });

            // Refresh data function
            window.refreshData = function() {
                // Add refresh button animation
                $('.refresh-btn button i').addClass('fa-spin');
                setTimeout(() => {
                    $('.refresh-btn button i').removeClass('fa-spin');
                }, 1000);

                // Show table loader immediately
                showTableLoader();

                // Update stats with real data
                updateStatsCards();

                // Reinitialize DataTable
                initializeDataTable();
            };

            // Show notification function
            function showNotification(message, type) {
                // Create notification element if it doesn't exist
                if ($('#notification').length === 0) {
                    $('body').append('<div id="notification" class="notification"><span id="notification-message"></span></div>');
                }

                const notification = $('#notification');
                const notificationMessage = $('#notification-message');

                // Set message and type
                notificationMessage.text(message);
                notification.attr('class', `notification ${type}`);

                // Clear any existing timeout
                if (notification.hideTimeout) {
                    clearTimeout(notification.hideTimeout);
                }

                // Show notification
                notification.addClass('show');

                // Hide notification after 4 seconds
                notification.hideTimeout = setTimeout(() => {
                    notification.removeClass('show');
                }, 4000);
            }

            // Table-specific loader functions
            function showTableLoader() {
                $(".table-loader-overlay").addClass("visible");
            }

            function hideTableLoader() {
                $(".table-loader-overlay").removeClass("visible");
            }

            // Make sure any existing loaders are hidden when the page loads
            $(window).on('load', function() {
                hideTableLoader();
                // Also hide the main loader
                hideLoader();
            });

            // Download Report Function
            async function downloadReport(requestId, buttonElement) {
                try {
                    const baseUrl = window.location.origin;
                    const downloadUrl = `${baseUrl}/api/reports?request_id=${requestId}&download=true`;

                    // Create a temporary link element for download
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = `report_${requestId}.xlsx`; // Default filename
                    link.style.display = 'none';

                    // Add to DOM, click, and remove
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Show success message
                    showPopupMessage('Report download started successfully!', 'success');

                } catch (error) {
                    console.error('Download error:', error);
                    showPopupMessage('Error downloading report. Please try again.', 'error');
                } finally {
                    // Reset button state
                    setTimeout(() => {
                        const icon = buttonElement.find('i');
                        icon.removeClass('fa-spinner fa-spin').addClass('fa-download');
                        buttonElement.removeClass('clicked');
                    }, 1000);
                }
            }

            // Custom Report Modal Functions
            function openCustomReportModal() {
                $('#customReportModal').addClass('active');
                document.body.style.overflow = 'hidden';
            }

            function closeCustomReportModal() {
                $('#customReportModal').removeClass('active');
                document.body.style.overflow = 'auto';
            }

            function resetFilters() {
                // Reset radio buttons to default (this_week)
                $('input[name="timeFilter"][value="this_week"]').prop('checked', true);

                // Hide custom date range
                $('#customDateRange').hide();

                // Clear all input fields
                $('#supplierIdFilter, #requestIdFilter, #requestGroupIdFilter').val('');
                $('#statusFilter').val('');
                $('#startDate, #endDate').val('');
            }

            function getDateRange(timeFilter, startDate = null, endDate = null) {
                const now = new Date();
                let start, end;

                switch (timeFilter) {
                    case 'this_week':
                        const startOfWeek = new Date(now);
                        startOfWeek.setDate(now.getDate() - now.getDay());
                        startOfWeek.setHours(0, 0, 0, 0);
                        start = startOfWeek.toISOString().split('T')[0];
                        end = now.toISOString().split('T')[0];
                        break;

                    case 'this_month':
                        start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
                        end = now.toISOString().split('T')[0];
                        break;

                    case 'last_month':
                        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
                        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
                        start = lastMonth.toISOString().split('T')[0];
                        end = lastMonthEnd.toISOString().split('T')[0];
                        break;

                    case 'this_year':
                        start = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
                        end = now.toISOString().split('T')[0];
                        break;

                    case 'custom':
                        start = startDate;
                        end = endDate;
                        break;

                    default:
                        start = null;
                        end = null;
                }

                return { start, end };
            }

            function generateReportFilename(filters) {
                const now = new Date();
                const timestamp = now.toISOString().split('T')[0];

                let filename = 'CustomReport';

                // Add time period to filename
                if (filters.timeFilter && filters.timeFilter !== 'custom') {
                    filename += `_${filters.timeFilter.replace('_', '')}`;
                } else if (filters.timeFilter === 'custom' && filters.startDate && filters.endDate) {
                    filename += `_${filters.startDate}_to_${filters.endDate}`;
                }

                // Add supplier if specified
                if (filters.supplierId) {
                    filename += `_Supplier${filters.supplierId}`;
                }

                // Add status if specified
                if (filters.status) {
                    filename += `_${filters.status}`;
                }

                filename += `_${timestamp}.xlsx`;

                return filename;
            }

            function validateFilters(filters) {
                // Validate custom date range if selected
                if (filters.timeFilter === 'custom') {
                    if (!filters.startDate || !filters.endDate) {
                        showPopupMessage('Please select both start and end dates for custom range.', 'error');
                        return false;
                    }

                    if (new Date(filters.startDate) > new Date(filters.endDate)) {
                        showPopupMessage('Start date cannot be after end date.', 'error');
                        return false;
                    }

                    // Check if date range is too far in the future
                    const today = new Date();
                    if (new Date(filters.startDate) > today) {
                        showPopupMessage('Start date cannot be in the future.', 'error');
                        return false;
                    }
                }

                // Validate numeric inputs
                if (filters.supplierId && (isNaN(filters.supplierId) || filters.supplierId < 0)) {
                    showPopupMessage('Supplier ID must be a valid positive number.', 'error');
                    return false;
                }

                if (filters.requestId && (isNaN(filters.requestId) || filters.requestId < 0)) {
                    showPopupMessage('Request ID must be a valid positive number.', 'error');
                    return false;
                }

                // Check if at least one filter is applied
                const hasFilters = filters.timeFilter !== 'this_week' ||
                                 filters.supplierId ||
                                 filters.requestId ||
                                 filters.requestGroupId ||
                                 filters.status;

                if (!hasFilters) {
                    const confirmGenerate = confirm('No specific filters applied. This will generate a report for this week. Continue?');
                    if (!confirmGenerate) {
                        return false;
                    }
                }

                return true;
            }

            async function downloadCustomReport() {
                try {
                    // Get form data
                    const formData = new FormData(document.getElementById('customReportForm'));
                    const filters = Object.fromEntries(formData.entries());

                    // Validate filters
                    if (!validateFilters(filters)) {
                        return;
                    }

                    // Get date range
                    const dateRange = getDateRange(filters.timeFilter, filters.startDate, filters.endDate);

                    // Build query parameters
                    const queryParams = new URLSearchParams();
                    queryParams.append('download', 'true');

                    if (dateRange.start) queryParams.append('start_date', dateRange.start);
                    if (dateRange.end) queryParams.append('end_date', dateRange.end);
                    if (filters.supplierId) queryParams.append('supplier_id', filters.supplierId);
                    if (filters.requestId) queryParams.append('request_id', filters.requestId);
                    if (filters.requestGroupId) queryParams.append('request_group_id', filters.requestGroupId);
                    if (filters.status) queryParams.append('status', filters.status);

                    // Generate filename
                    const filename = generateReportFilename(filters);
                    queryParams.append('filename', filename);

                    // Show loading state
                    const applyBtn = $('#applyFilters');
                    const originalText = applyBtn.html();
                    applyBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating Report...');

                    // Make API call
                    const baseUrl = window.location.origin;
                    const downloadUrl = `${baseUrl}/api/reports?${queryParams.toString()}`;

                    // Test API endpoint first
                    const testResponse = await fetch(downloadUrl.replace('download=true', 'download=false'));

                    if (!testResponse.ok) {
                        const errorData = await testResponse.json().catch(() => ({}));
                        throw new Error(errorData.message || `Server error: ${testResponse.status}`);
                    }

                    const testData = await testResponse.json();
                    if (!testData.data || testData.data.length === 0) {
                        showPopupMessage('No data found for the selected criteria. Please adjust your filters.', 'warning');
                        return;
                    }

                    // Create download link
                    const link = document.createElement('a');
                    link.href = downloadUrl;
                    link.download = filename;
                    link.style.display = 'none';

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // Show success message and close modal
                    showPopupMessage(`Custom report download started! Found ${testData.data.length} records.`, 'success');
                    closeCustomReportModal();

                } catch (error) {
                    console.error('Custom report download error:', error);

                    // Provide specific error messages
                    let errorMessage = 'Error generating custom report. Please try again.';
                    if (error.message.includes('404')) {
                        errorMessage = 'No data found for the selected criteria.';
                    } else if (error.message.includes('400')) {
                        errorMessage = 'Invalid filter parameters. Please check your inputs.';
                    } else if (error.message.includes('500')) {
                        errorMessage = 'Server error occurred. Please try again later.';
                    } else if (error.message.includes('Network')) {
                        errorMessage = 'Network error. Please check your connection.';
                    }

                    showPopupMessage(errorMessage, 'error');
                } finally {
                    // Reset button state
                    const applyBtn = $('#applyFilters');
                    applyBtn.prop('disabled', false).html('<i class="fas fa-download"></i> Generate Report');
                }
            }

            // Event Handlers
            $(document).ready(function() {
                // Custom Report Modal Events
                $('#customReportBtn').click(openCustomReportModal);
                $('#closeModal').click(closeCustomReportModal);
                $('#resetFilters').click(resetFilters);
                $('#applyFilters').click(downloadCustomReport);

                // Close modal when clicking outside
                $('#customReportModal').click(function(e) {
                    if (e.target === this) {
                        closeCustomReportModal();
                    }
                });

                // Handle custom date range toggle
                $('input[name="timeFilter"]').change(function() {
                    if ($(this).val() === 'custom') {
                        $('#customDateRange').slideDown(300);
                    } else {
                        $('#customDateRange').slideUp(300);
                    }
                });

                // Close modal with Escape key
                $(document).keydown(function(e) {
                    if (e.key === 'Escape' && $('#customReportModal').hasClass('active')) {
                        closeCustomReportModal();
                    }
                });
            });

            // Global loader functions (from base.html)
            function showLoader() {
                $(".loader-overlay").addClass("visible");
                $(".loader-overlay").fadeIn(300);
            }

            function hideLoader() {
                $(".loader-overlay").fadeOut(300, function() {
                    $(this).removeClass("visible");
                });
            }
        });
    </script>

    <style>
        /* Additional styles for the request list page */
        .table-row-hover:hover {
            background-color: rgba(67, 97, 238, 0.03);
        }

        .eye-icon.clicked, .download-icon.clicked {
            animation: click-animation 0.2s ease-in-out;
        }

        @keyframes click-animation {
            0% { transform: scale(1); }
            50% { transform: scale(0.9); }
            100% { transform: scale(1); }
        }

        /* Stage badge styling */
        .stage-badge {
            display: inline-flex;
            align-items: center;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius);
            font-size: 0.75rem;
            font-weight: var(--font-weight-bold);
            background-color: rgba(76, 201, 240, 0.1);
            color: var(--primary-dark);
        }

        /* File name styling */
        .file-name {
            max-width: 250px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
        }

        /* Text utilities */
        .text-muted {
            color: var(--gray-600);
        }

        .small {
            font-size: 0.8rem;
        }

        /* Table Container */
        .table-container {
            position: relative;
            border-radius: var(--border-radius-lg);
            background-color: white;
            overflow: hidden;
        }

        /* Table-specific loader styles */
        .table-loader-overlay {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 100;
            justify-content: center;
            align-items: center;
            border-radius: var(--border-radius-lg);
        }

        .table-loader-overlay.visible {
            display: flex;
        }

        .table-loader {
            border: 4px solid var(--gray-200);
            border-top: 4px solid var(--primary);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        /* Global loader styles (from base.html) */
        .loader-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loader-overlay.visible {
            display: flex;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Refresh button animation */
        .fa-spin {
            animation: fa-spin 1s linear infinite;
        }

        @keyframes fa-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: var(--spacing-md) var(--spacing-lg);
            border-radius: var(--border-radius);
            color: white;
            font-weight: var(--font-weight-bold);
            z-index: 1001;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity var(--transition), transform var(--transition);
            box-shadow: var(--shadow-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .notification::before {
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            font-size: 1.2rem;
        }

        .notification.success {
            background-color: var(--success);
        }

        .notification.success::before {
            content: "\f00c";
        }

        .notification.error {
            background-color: var(--danger);
        }

        .notification.error::before {
            content: "\f00d";
        }

        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* Improve table header alignment */
        table.dataTable thead th {
            text-align: left !important;
            vertical-align: middle !important;
            padding-top: 12px !important;
            padding-bottom: 12px !important;
        }

        /* Improve table cell alignment */
        table.dataTable tbody td {
            vertical-align: middle !important;
            padding: 12px !important;
        }

        /* Center the action column */
        .text-center {
            text-align: center !important;
        }

        /* Improve pagination buttons */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            font-weight: bold;
        }

        /* Ensure pagination numbers are displayed in increasing order */
        .dataTables_wrapper .dataTables_paginate {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 5px;
        }

        /* Improve search box */
        .dataTables_wrapper .dataTables_filter input {
            background-color: white;
            padding: 8px 12px;
            border: 1px solid var(--gray-300);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-sm);
        }

        /* Improve length selector */
        .dataTables_wrapper .dataTables_length select {
            padding: 6px 10px;
            border-radius: var(--border-radius);
            border: 1px solid var(--gray-300);
            background-color: white;
            box-shadow: var(--shadow-sm);
        }

        /* Removed stats card number animation */
    </style>

    {% endblock %}
{% endblock %}